<template>
	<view class="contact-container">
		<view class="contact-content">
			<!-- 客服信息展示 -->
			<view class="service-info">
				<view class="service-header">
					<image class="service-avatar" src="/static/images/service-avatar.png" mode="aspectFill"></image>
					<view class="service-details">
						<text class="service-name">容易购客服</text>
						<text class="service-status">在线服务</text>
					</view>
				</view>
				
				<view class="service-description">
					<text class="desc-title">我们的服务时间</text>
					<text class="desc-content">周一至周日 9:00-18:00</text>
					<text class="desc-title">服务内容</text>
					<text class="desc-content">• 商品咨询与购买指导</text>
					<text class="desc-content">• 店铺入驻与管理</text>
					<text class="desc-content">• 技术支持与问题反馈</text>
					<text class="desc-content">• 其他相关服务</text>
				</view>
			</view>
			
			<!-- 快捷问题 -->
			<view class="quick-questions">
				<text class="section-title">常见问题</text>
				<view class="question-list">
					<view class="question-item" @click="selectQuestion('如何注册店铺？')">
						<text class="question-text">如何注册店铺？</text>
					</view>
					<view class="question-item" @click="selectQuestion('如何发布商品？')">
						<text class="question-text">如何发布商品？</text>
					</view>
					<view class="question-item" @click="selectQuestion('如何联系买家？')">
						<text class="question-text">如何联系买家？</text>
					</view>
					<view class="question-item" @click="selectQuestion('账号相关问题')">
						<text class="question-text">账号相关问题</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部客服按钮 -->
		<view class="contact-bottom">
			<!-- #ifdef MP-WEIXIN -->
			<button 
				class="contact-btn" 
				open-type="contact"
				@contact="handleContact"
				session-from="mine-contact"
			>
				<text class="btn-text">联系客服</text>
			</button>
			<!-- #endif -->
			
			<!-- #ifndef MP-WEIXIN -->
			<view class="contact-btn disabled">
				<text class="btn-text">请在微信小程序中使用</text>
			</view>
			<!-- #endif -->
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			
		}
	},
	methods: {
		// 选择快捷问题
		selectQuestion(question) {
			uni.showModal({
				title: '提示',
				content: `您选择了"${question}"，点击下方"联系客服"按钮可直接向客服咨询此问题`,
				showCancel: false
			});
		},
		
		// 处理客服会话
		handleContact(e) {
			console.log('客服会话事件:', e);
			// 这里可以处理客服会话的相关逻辑
			// 比如记录用户咨询的问题类型等
		}
	}
}
</script>

<style lang="scss">
.contact-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.contact-content {
	flex: 1;
	padding: 20rpx;
}

.service-info {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	
	.service-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		
		.service-avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 40rpx;
			margin-right: 20rpx;
		}
		
		.service-details {
			flex: 1;
			
			.service-name {
				display: block;
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.service-status {
				font-size: 24rpx;
				color: #52c41a;
			}
		}
	}
	
	.service-description {
		.desc-title {
			display: block;
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 12rpx;
			margin-top: 20rpx;
			
			&:first-child {
				margin-top: 0;
			}
		}
		
		.desc-content {
			display: block;
			font-size: 26rpx;
			color: #666;
			line-height: 1.5;
			margin-bottom: 8rpx;
		}
	}
}

.quick-questions {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	
	.section-title {
		display: block;
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.question-list {
		.question-item {
			padding: 20rpx;
			background-color: #f8f9fa;
			border-radius: 8rpx;
			margin-bottom: 12rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.question-text {
				font-size: 28rpx;
				color: #333;
			}
			
			&:active {
				background-color: #e9ecef;
			}
		}
	}
}

.contact-bottom {
	padding: 20rpx;
	background-color: #fff;
	border-top: 1px solid #e5e5e5;
	
	.contact-btn {
		width: 100%;
		height: 88rpx;
		background-color: #2979ff;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		
		&.disabled {
			background-color: #ccc;
		}
		
		.btn-text {
			font-size: 32rpx;
			color: #fff;
			font-weight: 500;
		}
		
		&::after {
			border: none;
		}
	}
}
</style>
