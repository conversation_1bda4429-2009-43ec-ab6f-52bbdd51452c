import request from '@/utils/request'

// 查询用户业务名片列表
export function listUserCard(query) {
  return request({
    url: '/buy/userCard/list',
    method: 'get',
    params: query
  })
}

// 查询用户业务名片详细
export function getUserCard(id) {
  return request({
    url: '/buy/userCard/' + id,
    method: 'get'
  })
}

// 新增用户业务名片
export function addUserCard(data) {
  return request({
    url: '/buy/userCard',
    method: 'post',
    data: data
  })
}

// 修改用户业务名片
export function updateUserCard(data) {
  return request({
    url: '/buy/userCard',
    method: 'put',
    data: data
  })
}

// 删除用户业务名片
export function delUserCard(id) {
  return request({
    url: '/buy/userCard/' + id,
    method: 'delete'
  })
}
