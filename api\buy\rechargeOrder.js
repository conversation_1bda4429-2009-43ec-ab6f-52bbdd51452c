import request from '@/utils/request'

// 查询店铺充值订单列表
export function listRechargeOrder(query) {
  return request({
    url: '/buy/rechargeOrder/list',
    method: 'get',
    params: query
  })
}

// 查询店铺充值订单详细
export function getRechargeOrder(orderId) {
  return request({
    url: '/buy/rechargeOrder/' + orderId,
    method: 'get'
  })
}

// 新增店铺充值订单
export function addRechargeOrder(data) {
  return request({
    url: '/buy/rechargeOrder',
    method: 'post',
    data: data
  })
}

// 修改店铺充值订单
export function updateRechargeOrder(data) {
  return request({
    url: '/buy/rechargeOrder',
    method: 'put',
    data: data
  })
}

// 删除店铺充值订单
export function delRechargeOrder(orderId) {
  return request({
    url: '/buy/rechargeOrder/' + orderId,
    method: 'delete'
  })
}

// 获取付款参数
export function payOrder(orderId) {
  return request({
    url: '/buy/rechargeOrder/pay/' + orderId
  })
}
