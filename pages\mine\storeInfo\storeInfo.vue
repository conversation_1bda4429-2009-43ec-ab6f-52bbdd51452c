<template>
	<view class="page-container">
		<!-- 审核状态显示 -->
		<view v-if="isEdit" class="audit-status-section">
			<view class="status-card" :class="getStatusCardClass">
				<view class="status-info">
					<u-icon :name="getStatusIcon" :color="getStatusIconColor" size="24"></u-icon>
					<view class="status-text">
						<text class="status-title">{{ getStatusTitle }}</text>
						<text class="status-desc">{{ getStatusDesc }}</text>
					</view>
				</view>
				<view v-if="form.auditStatus === '2'" class="fail-reason">
					<text class="reason-label">未通过原因：</text>
					<text class="reason-text">{{ form.audit_remark || '无' }}</text>
				</view>
			</view>
		</view>

		<!-- 审核未通过提示 -->
		<view v-if="form.auditStatus === '2'" class="audit-fail-tip">
			<u-icon name="info-circle-fill" color="#fa3534" size="18"></u-icon>
			<text class="tip-text">审核未通过原因：{{ form.audit_remark || '无' }}</text>
		</view>

		<!-- 审核中遮罩 -->
		<view v-if="form.auditStatus === '0' && isEdit" class="audit-pending-overlay">
			<view class="pending-box">
				<u-icon name="info-circle-fill" color="#2979ff" size="50"></u-icon>
				<text class="pending-text">您的店铺信息正在审核中，请耐心等待...</text>
			</view>
		</view>

		<u-form :model="form" ref="uForm" :disabled="isFormDisabled" label-position="top" label-width="auto">
			<!-- 基本信息 -->
			<view class="form-block">
				<u-form-item label="店铺头像" prop="avatar" required>
					<u-upload
						:file-list="avatarList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="avatar"
						:max-count="1"
						:action="uploadUrl"
						:header="header"
						:disabled="isFormDisabled"
					></u-upload>
				</u-form-item>
				<u-form-item label="店铺名称" prop="storeName" required>
					<u-input v-model="form.storeName" placeholder="请输入店铺名称" border="bottom" :disabled="isFormDisabled" />
				</u-form-item>
				<u-form-item label="品牌" prop="brand">
					<u-input v-model="form.brand" placeholder="请输入品牌" border="bottom" :disabled="isFormDisabled" />
				</u-form-item>
				<u-form-item label="主营业务" prop="mainBusiness">
					<u-textarea
						v-model="form.mainBusiness"
						placeholder="请详细描述您的主营业务范围，如：五金工具批发、建材销售等"
						:auto-height="true"
						:maxlength="2000"
						count
						height="120"
						border="bottom"
						:disabled="isFormDisabled"
					></u-textarea>
				</u-form-item>
				<u-form-item label="联系人" prop="contactPerson" required>
					<u-input v-model="form.contactPerson" placeholder="请输入联系人" border="bottom" :disabled="isFormDisabled" />
				</u-form-item>
				<u-form-item label="联系电话" prop="phone" required>
					<u-input v-model="form.phone" type="number" placeholder="请输入联系电话" border="bottom" :disabled="isFormDisabled" />
				</u-form-item>

			</view>

			<!-- 媒体信息 -->
			<view class="form-block">
				<u-form-item label="店铺展示图" prop="displayImages">
					<u-upload
						:file-list="displayImagesList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="displayImages"
						multiple
						:max-count="9"
						:action="uploadUrl"
						:header="header"
						:disabled="isFormDisabled"
					></u-upload>
				</u-form-item>
				<u-form-item label="库房/工厂大图" prop="factoryImages">
					<u-upload
						:file-list="factoryImagesList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="factoryImages"
						multiple
						:max-count="9"
						:action="uploadUrl"
						:header="header"
						:disabled="isFormDisabled"
					></u-upload>
				</u-form-item>
				<u-form-item label="库房/工厂展示图" prop="factoryDisplayImages">
					<u-upload
						:file-list="factoryDisplayImagesList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="factoryDisplayImages"
						multiple
						:max-count="9"
						:action="uploadUrl"
						:header="header"
						:disabled="isFormDisabled"
					></u-upload>
				</u-form-item>
				<u-form-item label="店铺资质图片" prop="qualificationImages">
					<u-upload
						:file-list="qualificationImagesList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="qualificationImages"
						:max-count="1"
						:action="uploadUrl"
						:header="header"
						:disabled="isFormDisabled"
					></u-upload>
				</u-form-item>
				<u-form-item label="店铺微信二维码" prop="wechatQrcode">
					<u-upload
						:file-list="wechatQrcodeList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="wechatQrcode"
						:max-count="1"
						:action="uploadUrl"
						:header="header"
						:disabled="isFormDisabled"
					></u-upload>
				</u-form-item>
				<u-form-item label="店铺视频" prop="videoUrl">
					<u-upload
						:file-list="videoList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="videoUrl"
						multiple
						:max-count="5"
						:action="uploadUrl"
						:header="header"
						:disabled="isFormDisabled"
						accept="video"
					></u-upload>
					<view class="upload-tip">
						<text>最多可上传5个视频，支持mp4格式，建议单个视频大小不超过50MB</text>
					</view>
				</u-form-item>
			</view>
			
			<!-- 位置信息 -->
			<view class="form-block">
				<u-form-item label="市场及街区" prop="marketAddress">
					<view class="market-selector" @click="!isFormDisabled && showMarketSelector()">
						<text class="market-text" :class="{'placeholder': !form.marketAddress, 'disabled': isFormDisabled}">
							{{ form.marketAddress || '点击选择市场及街区' }}
						</text>
						<u-icon name="arrow-right" size="16" :color="isFormDisabled ? '#C8C7CC' : '#C8C7CC'"></u-icon>
					</view>
				</u-form-item>

				<u-form-item label="详细地址" prop="address">
					<u-input v-model="form.address" placeholder="请输入详细地址" border="bottom" :disabled="isFormDisabled" />
				</u-form-item>
				<!-- 经纬度输入框 -->
				<u-form-item label="经纬度" prop="location">
					<view class="location-container">
						<view class="location-inputs">
							<u-input class="location-input" type="number" v-model="form.latitude" placeholder="纬度" border="bottom" :disabled="isFormDisabled" />
							<u-input class="location-input" type="number" v-model="form.longitude" placeholder="经度" border="bottom" :disabled="isFormDisabled" />
						</view>
						<view class="location-btn">
							<u-button type="primary" size="mini" plain @click="handleGetLocation" :disabled="isFormDisabled">
								<u-icon name="map-fill" size="14" style="margin-right: 4px;"></u-icon>选择
							</u-button>
						</view>
					</view>
				</u-form-item>
			</view>
		</u-form>
		
		<!-- 底部操作按钮 -->
		<view class="button-wrapper">
			<u-button type="primary" :text="submitButtonText" @click="submitForm" :disabled="isSubmitDisabled"></u-button>
		</view>

		<!-- 市场选择器 -->
		<u-picker
			:show="showMarketPicker"
			:columns="marketPickerColumns"
			@change="onMarketPickerChange"
			@confirm="onMarketPickerConfirm"
			@cancel="onMarketPickerCancel"
			:defaultIndex="[selectedCityIndex, selectedMarketIndex]"
		></u-picker>



	</view>
</template>

<script>
	import { listStoreInfo, addStoreInfo, updateStoreInfo } from "@/api/buy/storeInfo.js";
	import { getToken } from "@/utils/auth";
	import { listNodes } from "@/api/buy/nodes.js"
	import { baseUrl } from "@/config.js";
	export default {
		data() {
			return {
				// 按要求获取 userId 和 token
				userId: this.$store.state.user.userId,
				header: { Authorization: 'Bearer ' + getToken() },
				
				// 后端统一上传地址，请替换为您自己的
				uploadUrl: '',
				baseUrl,
				isEdit: false,
				form: {
					storeId: null,
					storeName: '',
					avatar: '',
					brand: '',
					contactPerson: '',
					phone: '',
					mainBusiness: '', // 主营业务描述
					marketAddress: '',
					address: '',
					longitude: '',
					latitude: '',
					displayImages: '',
					factoryImages: '',
					factoryDisplayImages: '',
					qualificationImages: '',
					wechatQrcode: '',
					videoUrl: '', // 店铺视频
					auditStatus: null, // 0-待审核 1-通过 2-不通过
					audit_remark: '',
				},
				// u-upload 的 file-list
				avatarList: [],
				displayImagesList: [],
				factoryImagesList: [],
				factoryDisplayImagesList: [],
				qualificationImagesList: [],
				wechatQrcodeList: [],
				videoList: [],

				// 二级选择器相关数据
				nodesData: [], // 原始节点数据
				cityList: [], // 城市列表
				marketList: [], // 市场列表（根据选中的城市动态更新）
				showMarketPicker: false, // 显示选择器
				marketPickerColumns: [], // 选择器列数据
				selectedCityIndex: 0, // 选中的城市索引
				selectedMarketIndex: 0, // 选中的市场索引



				rules: {
					'storeName': { type: 'string', required: true, message: '请输入店铺名称', trigger: ['blur', 'change'] },
					'contactPerson': { type: 'string', required: true, message: '请输入联系人', trigger: ['blur', 'change'] },
					'phone': { type: 'string', required: true, message: '请输入联系电话', trigger: ['blur', 'change'] },
					'avatar': { type: 'string', required: true, message: '请上传店铺头像', trigger: ['blur', 'change'] },
				}
			}
		},
		computed: {

			submitButtonText() {
				if (!this.isEdit) return '提交审核';
				if (this.form.auditStatus === 0 || this.form.auditStatus === '0') return '审核中...';
				if (this.form.auditStatus === 1 || this.form.auditStatus === '1') return '修改信息 (将重新审核)';
				if (this.form.auditStatus === 2 || this.form.auditStatus === '2') return '重新提交审核';
				return '提交';
			},
			// 表单是否禁用
			isFormDisabled() {
				return this.isEdit && (this.form.auditStatus === 0 || this.form.auditStatus === '0');
			},
			// 提交按钮是否禁用
			isSubmitDisabled() {
				return this.isEdit && (this.form.auditStatus === 0 || this.form.auditStatus === '0');
			},
			// 审核状态卡片样式类
			getStatusCardClass() {
				const status = this.form.auditStatus;
				switch(status) {
					case 0:
					case '0':
						return 'status-pending';
					case 1:
					case '1':
						return 'status-approved';
					case 2:
					case '2':
						return 'status-rejected';
					default:
						return 'status-unknown';
				}
			},
			// 状态图标
			getStatusIcon() {
				const status = this.form.auditStatus;
				switch(status) {
					case 0:
					case '0':
						return 'clock';
					case 1:
					case '1':
						return 'checkmark-circle-fill';
					case 2:
					case '2':
						return 'close-circle-fill';
					default:
						return 'info-circle';
				}
			},
			// 状态图标颜色
			getStatusIconColor() {
				const status = this.form.auditStatus;
				switch(status) {
					case 0:
					case '0':
						return '#ff9500';
					case 1:
					case '1':
						return '#19be6b';
					case 2:
					case '2':
						return '#fa3534';
					default:
						return '#909399';
				}
			},
			// 状态标题
			getStatusTitle() {
				const status = this.form.auditStatus;
				switch(status) {
					case 0:
					case '0':
						return '审核中';
					case 1:
					case '1':
						return '审核通过';
					case 2:
					case '2':
						return '审核未通过';
					default:
						return '未知状态';
				}
			},
			// 状态描述
			getStatusDesc() {
				const status = this.form.auditStatus;
				switch(status) {
					case 0:
					case '0':
						return '您的店铺申请正在审核中，请耐心等待';
					case 1:
					case '1':
						return '恭喜！您的店铺已通过审核并正常营业';
					case 2:
					case '2':
						return '很遗憾，您的店铺申请未通过审核';
					default:
						return '';
				}
			}
		},
		created() {
			// 构建上传URL
			this.initUploadUrl();
		},
		onReady() {
			// 需要在onReady中设置规则，否则可能不生效
			this.$refs.uForm.setRules(this.rules)
		},
		onLoad() {
			listNodes({pageSize:100000}).then(res=>{
				console.log('获取节点数据:', res);
				if (res.code === 200 && res.rows) {
					this.nodesData = res.rows;
					console.log('原始节点数据:', this.nodesData);
					console.log('数据样本:', this.nodesData.slice(0, 3));
					this.initPickerData();
					// 在节点数据加载完成后再获取店铺数据
					if(this.userId){
						this.getStoreData();
					}
				}
			}).catch(error => {
				console.error('获取节点数据失败:', error);
				// 即使节点数据加载失败，也要获取店铺数据
				if(this.userId){
					this.getStoreData();
				}
			})
		},
		methods: {
			// 初始化上传URL
			initUploadUrl() {
				const baseUrl = process.env.VUE_APP_BASE_API;
				console.log('环境变量 VUE_APP_BASE_API:', baseUrl);

				if (baseUrl && baseUrl !== 'undefined' && baseUrl.trim() !== '') {
					this.uploadUrl = baseUrl + '/common/upload';
				} else {
					// 如果环境变量未设置，从config.js获取
					try {
						const config = require('@/config.js');
						console.log('从config.js获取配置:', config);
						if (config && config.baseUrl) {
							this.uploadUrl = config.baseUrl + '/common/upload';
						} else {
							this.uploadUrl = 'http://localhost:8080/common/upload'; // 默认地址
						}
					} catch (error) {
						console.error('读取config.js失败:', error);
						this.uploadUrl = 'http://localhost:8080/common/upload'; // 默认地址
					}
				}

				console.log('最终上传URL配置:', this.uploadUrl);

				// 验证URL格式
				if (!this.uploadUrl.startsWith('http://') && !this.uploadUrl.startsWith('https://')) {
					console.error('上传URL格式不正确:', this.uploadUrl);
				}
			},

			// 初始化选择器数据
			initPickerData() {
				console.log('开始初始化选择器数据，原始数据长度:', this.nodesData.length);
				console.log('数据样本:', this.nodesData.slice(0, 5));

				// 获取所有城市（parentId为null或0且nodeType为city）
				this.cityList = this.nodesData.filter(item => {
					const parentId = item.parent_id || item.parentId; // 兼容两种字段名
					// 根据数据库表结构，顶级节点的parent_id为NULL
					const isTopLevel = parentId === null || parentId === undefined || parentId === 0 || parentId === '0';
					const isCity = item.node_type === 'city' || item.nodeType === 'city'; // 兼容两种字段名
					console.log(`节点 ${item.name}: parent_id=${parentId}, node_type=${item.node_type || item.nodeType}, isTopLevel=${isTopLevel}, isCity=${isCity}`);
					return isTopLevel && isCity;
				});
				console.log('过滤后的城市列表:', this.cityList);

				// 初始化第一个城市的市场列表
				if (this.cityList.length > 0) {
					this.updateMarketList(this.cityList[0].id);
				} else {
					console.log('没有找到城市数据');
					this.marketList = [];
				}

				this.updatePickerColumns();
				console.log('最终选择器列数据:', this.marketPickerColumns);
			},
			
			// 更新市场列表
			updateMarketList(cityId) {
				console.log(`开始更新城市ID ${cityId} 的市场列表`);
				this.marketList = this.nodesData.filter(item => {
					// 兼容两种字段名
					const itemParentId = item.parent_id || item.parentId;
					const nodeType = item.node_type || item.nodeType;

					// 确保类型一致的比较
					const itemParentIdStr = String(itemParentId);
					const targetCityIdStr = String(cityId);
					const isMarket = nodeType === 'market';
					const isChild = itemParentIdStr === targetCityIdStr;

					console.log(`节点 ${item.name}: parent_id=${itemParentId}(${itemParentIdStr}), 目标cityId=${cityId}(${targetCityIdStr}), node_type=${nodeType}, isChild=${isChild}, isMarket=${isMarket}`);
					return isChild && isMarket;
				});
				console.log(`城市ID ${cityId} 的市场列表:`, this.marketList);
			},
			
			// 更新选择器列数据
			updatePickerColumns() {
				this.marketPickerColumns = [
					this.cityList.map(city => city.name),
					this.marketList.length > 0 ? this.marketList.map(market => market.name) : ['暂无市场']
				];
			},
			
			// 显示市场选择器
			showMarketSelector() {
				console.log('点击显示市场选择器');
				console.log('当前城市列表:', this.cityList);
				console.log('当前市场列表:', this.marketList);
				console.log('选择器列数据:', this.marketPickerColumns);
				
				if (this.cityList.length === 0) {
					this.$modal.msgError('暂无可选择的市场数据');
					return;
				}
				this.showMarketPicker = true;
				console.log('显示选择器状态:', this.showMarketPicker);
			},
			
			// 选择器列改变事件
			onMarketPickerChange(e) {
				const { columnIndex, index } = e;
				
				if (columnIndex === 0) {
					// 城市改变，更新市场列表
					this.selectedCityIndex = index;
					this.selectedMarketIndex = 0; // 重置市场索引
					this.updateMarketList(this.cityList[index].id);
					this.updatePickerColumns();
				} else if (columnIndex === 1) {
					// 市场改变
					this.selectedMarketIndex = index;
				}
			},
			
			// 确认选择
			onMarketPickerConfirm(e) {
				console.log('选择器确认事件:', e);
				const { indexs } = e;
				this.selectedCityIndex = indexs[0];
				this.selectedMarketIndex = indexs[1];

				const selectedCity = this.cityList[this.selectedCityIndex];
				const selectedMarket = this.marketList[this.selectedMarketIndex];

				console.log('选中的城市:', selectedCity);
				console.log('选中的市场:', selectedMarket);

				if (selectedCity && selectedMarket) {
					this.form.marketAddress = `${selectedCity.name} - ${selectedMarket.name}`;
				} else if (selectedCity && this.marketList.length === 0) {
					this.form.marketAddress = selectedCity.name;
					this.$modal.msgError('该城市暂无可选市场');
				}

				this.showMarketPicker = false;
			},
			
			// 取消选择
			onMarketPickerCancel() {
				this.showMarketPicker = false;
			},


			
			getStoreData() {
				this.$modal.loading("加载中...");
				listStoreInfo({ userId: this.userId }).then(res => {
					this.$modal.closeLoading();
					if (res.code === 200 && res.rows.length > 0) {
						this.isEdit = true;
						this.form = res.rows[0];
						this.initFileLists();

						if (this.form.auditStatus === 0 || this.form.auditStatus === '0') uni.setNavigationBarTitle({ title: '审核中' });
						else if (this.form.auditStatus === 1 || this.form.auditStatus === '1') uni.setNavigationBarTitle({ title: '店铺管理' });
						else if (this.form.auditStatus === 2 || this.form.auditStatus === '2') uni.setNavigationBarTitle({ title: '审核未通过' });
					} else {
						this.isEdit = false;
						uni.setNavigationBarTitle({ title: '店铺入驻申请' });
					}
				}).catch(() => {
					this.$modal.closeLoading();
					this.$modal.msgError("数据加载失败");
				});
			},
			
			submitForm() {
				// 检查是否被禁用
				if (this.isSubmitDisabled) {
					this.$modal.msgError('审核中不允许提交');
					return;
				}
				
				this.$refs.uForm.validate().then(res => {
					this.$modal.loading("提交中...");
					const api = this.isEdit ? updateStoreInfo : addStoreInfo;
					const successMsg = this.isEdit ? '修改成功，已重新提交审核' : '提交成功，请等待审核';

					if (this.isEdit) {
						this.form.auditStatus = 0; // 重新提交审核
					} else {
						this.form.userId = this.userId;
					}
					
					api(this.form).then(response => {
						this.$modal.closeLoading();
						this.$modal.msgSuccess(successMsg);
						setTimeout(() => uni.navigateBack(), 1500);
					}).catch(() => {
						this.$modal.closeLoading();
					});
				});
			},

			handleGetLocation() {
				// 如果表单被禁用，不允许操作
				if (this.isFormDisabled) {
					this.$modal.msgError('审核中不允许修改位置信息');
					return;
				}
				
				// 检查是否在微信小程序环境
				// #ifdef MP-WEIXIN
				// 先检查权限
				wx.getSetting({
					success: (settingRes) => {
						if (settingRes.authSetting['scope.userLocation'] === false) {
							// 用户之前拒绝了权限，需要引导用户去设置页面
							wx.showModal({
								title: '提示',
								content: '需要获取您的位置信息，请在设置中开启位置权限',
								confirmText: '去设置',
								success: (modalRes) => {
									if (modalRes.confirm) {
										wx.openSetting();
									}
								}
							});
							return;
						}
						
						// 调用选择位置
						wx.chooseLocation({
							success: (res) => {
								console.log('选择地址成功:', res);
								// 只填入经纬度，详细地址和市场街区由用户手动输入
								this.form.latitude = res.latitude.toString();
								this.form.longitude = res.longitude.toString();
								
								this.$modal.msgSuccess('经纬度获取成功');
							},
							fail: (err) => {
								console.error('选择地址失败:', err);
								if (err.errMsg && (err.errMsg.includes('cancel') || err.errMsg.includes('用户取消'))) {
									// 用户取消选择
									return;
								}
								
								// 如果是权限问题，给出更明确的提示
								if (err.errMsg && (err.errMsg.includes('auth') || err.errMsg.includes('requiredPrivateInfos'))) {
									this.$modal.msgError('请先在小程序设置中同意位置权限');
								} else {
									this.$modal.msgError('地址选择失败：' + (err.errMsg || '未知错误'));
								}
							}
						});
					},
					fail: (err) => {
						console.error('获取设置失败:', err);
						this.$modal.msgError('获取权限设置失败');
					}
				});
				// #endif
				
				// #ifndef MP-WEIXIN
				// 非微信小程序环境，使用uni.chooseLocation
				uni.chooseLocation({
					success: (res) => {
						console.log('选择地址成功:', res);
						// 只填入经纬度，详细地址和市场街区由用户手动输入
						this.form.latitude = res.latitude.toString();
						this.form.longitude = res.longitude.toString();
						
						this.$modal.msgSuccess('经纬度获取成功');
					},
					fail: (err) => {
						console.error('选择地址失败:', err);
						if (err.errMsg && err.errMsg.includes('cancel')) {
							return;
						}
						this.$modal.msgError('地址选择失败：' + (err.errMsg || '未知错误'));
					}
				});
				// #endif
			},

			// -------------------- uView Upload Handlers --------------------
			// 初始化文件列表
			initFileLists() {
				this.avatarList = this.urlsToArray(this.form.avatar);
				this.displayImagesList = this.urlsToArray(this.form.displayImages);
				this.factoryImagesList = this.urlsToArray(this.form.factoryImages);
				this.factoryDisplayImagesList = this.urlsToArray(this.form.factoryDisplayImages);
				this.qualificationImagesList = this.urlsToArray(this.form.qualificationImages);
				this.wechatQrcodeList = this.urlsToArray(this.form.wechatQrcode);
				this.videoList = this.urlsToArray(this.form.videoUrl);
			},
			urlsToArray(urls) {
				if (!urls) return [];
				return urls.split(',').map(url => {
					// 如果URL不是完整的http/https链接，则拼接baseUrl
					if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
						return { url: this.baseUrl + url };
					}
					return { url };
				});
			},
			// 从表单的...List属性更新form的URL字符串
			updateFormUrls(fieldName) {
				let listName = `${fieldName}List`;
				// 特殊处理videoUrl，对应的列表名是videoList
				if (fieldName === 'videoUrl') {
					listName = 'videoList';
				}
				const list = this[listName];
				this.form[fieldName] = list.map(file => {
					// 如果URL包含baseUrl，则去掉baseUrl只保存相对路径
					if (file.url && file.url.startsWith(this.baseUrl)) {
						return file.url.replace(this.baseUrl, '');
					}
					return file.url;
				}).join(',');
			},

			// 新增图片
			async afterRead(event) {
				// 如果表单被禁用，不允许上传
				if (this.isFormDisabled) {
					this.$modal.msgError('审核中不允许上传文件');
					return;
				}

				// 显示上传加载动画
				const fileType = event.name === 'videoUrl' ? '视频' : '图片';
				this.$modal.loading(`正在上传${fileType}...`);

				console.log('afterRead event:', event);
				// 获取当前操作的 file-list
				let listName = `${event.name}List`;
				// 特殊处理videoUrl，对应的列表名是videoList
				if (event.name === 'videoUrl') {
					listName = 'videoList';
				}
				let fileList = this[listName];

				console.log('listName:', listName, 'fileList:', fileList);
				
				// 处理文件数据，确保是数组格式
				let files = [];
				if (Array.isArray(event.file)) {
					files = event.file;
				} else if (event.file) {
					files = [event.file];
				} else {
					console.error('未找到文件数据');
					return;
				}
				
				// 将新选择的图片添加到列表中
				files.forEach(item => {
					fileList.push({ ...item, status: 'uploading', message: '上传中' });
				});
				
				// 检查上传URL是否有效，如果无效则重新初始化
				if (!this.uploadUrl || !this.uploadUrl.startsWith('http')) {
					console.log('上传URL无效，重新初始化:', this.uploadUrl);
					this.initUploadUrl();

					// 再次检查
					if (!this.uploadUrl || !this.uploadUrl.startsWith('http')) {
						this.$modal.msgError('上传地址配置错误，请联系管理员');
						this.$modal.closeLoading();
						// 将所有文件状态设置为失败
						files.forEach(item => {
							const targetItem = fileList.find(f => f === item);
							if (targetItem) {
								targetItem.status = 'failed';
								targetItem.message = '上传地址未配置';
							}
						});
						return;
					}
				}
				
				// 遍历并上传
				for (let i = 0; i < fileList.length; i++) {
					const item = fileList[i];
					if (item.status !== 'uploading') continue; // 只上传状态为 uploading 的文件
					
					try {
						console.log('开始上传文件:', item.url, '到:', this.uploadUrl);
						const result = await uni.uploadFile({
							url: this.uploadUrl,
							filePath: item.url,
							name: 'file',
							header: this.header
						});
						
						console.log('上传结果:', result);
						
						// 处理上传结果，可能是数组格式
						let uploadResult = result;
						if (Array.isArray(result)) {
							console.log('上传结果是数组格式，长度:', result.length);
							if (result.length > 1) {
								// 如果结果是数组，取第二个元素（第一个可能是null）
								uploadResult = result[1] || result[0];
								console.log('使用数组中的第二个元素:', uploadResult);
							} else if (result.length === 1) {
								uploadResult = result[0];
								console.log('使用数组中的第一个元素:', uploadResult);
							}
						}
						
						// 检查返回数据是否有效
						if (!uploadResult || !uploadResult.data) {
							console.error('上传返回数据为空:', uploadResult);
							item.status = 'failed';
							item.message = '服务器未返回数据';
							continue;
						}
						
						let responseData;
						try {
							responseData = JSON.parse(uploadResult.data);
							console.log('解析后的响应数据:', responseData);
						} catch (parseError) {
							console.error('JSON解析失败:', parseError);
							console.error('原始数据:', uploadResult.data);
							item.status = 'failed';
							item.message = 'JSON解析失败';
							continue;
						}
						
						if (responseData.code === 200) {
							item.status = 'success';
							item.message = '';
							// 检查返回数据中是否有fileName字段，如果有则拼接baseUrl
							if (responseData.fileName) {
								item.url = this.baseUrl + responseData.fileName;
								console.log('上传成功，拼接后的文件URL:', item.url);
							} else if (responseData.url) {
								item.url = responseData.url;
								console.log('上传成功，直接使用返回的URL:', responseData.url);
							} else {
								item.status = 'failed';
								item.message = '服务器未返回有效的文件路径';
								console.log('上传失败: 服务器未返回fileName或url字段');
							}
						} else {
							item.status = 'failed';
							item.message = responseData.msg || '上传失败';
							console.log('上传失败:', responseData.msg);
						}
					} catch (e) {
						console.error('上传失败:', e);
						item.status = 'failed';
						item.message = '上传异常';
					}
				}
				this.updateFormUrls(event.name);

				// 关闭加载动画
				this.$modal.closeLoading();
			},

			// 删除图片
			deletePic(event) {
				// 如果表单被禁用，不允许删除
				if (this.isFormDisabled) {
					this.$modal.msgError('审核中不允许删除图片');
					return;
				}

				let listName = `${event.name}List`;
				// 特殊处理videoUrl，对应的列表名是videoList
				if (event.name === 'videoUrl') {
					listName = 'videoList';
				}
				this[listName].splice(event.index, 1);
				this.updateFormUrls(event.name);
			},
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 10px 10px 80px 10px; // 底部留出空间给按钮
	}

	// 审核状态区域样式
	.audit-status-section {
		margin-bottom: 12px;
		
		.status-card {
			background-color: #ffffff;
			border-radius: 8px;
			padding: 15px;
			box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
			
			.status-info {
				display: flex;
				align-items: center;
				margin-bottom: 10px;
				
				.status-text {
					margin-left: 12px;
					flex: 1;
					
					.status-title {
						display: block;
						font-size: 16px;
						font-weight: bold;
						margin-bottom: 4px;
					}
					
					.status-desc {
						display: block;
						font-size: 14px;
						color: #666;
						line-height: 1.4;
					}
				}
			}
			
			.fail-reason {
				padding-top: 10px;
				border-top: 1px solid #f0f0f0;
				
				.reason-label {
					font-size: 14px;
					color: #666;
				}
				
				.reason-text {
					font-size: 14px;
					color: #fa3534;
					margin-left: 8px;
				}
			}
			
			// 不同审核状态的边框颜色
			&.status-pending {
				border-left: 4px solid #ff9500;
			}
			
			&.status-approved {
				border-left: 4px solid #19be6b;
			}
			
			&.status-rejected {
				border-left: 4px solid #fa3534;
			}
		}
	}

	.form-block {
		background-color: #ffffff;
		border-radius: 8px;
		padding: 5px 15px;
		margin-bottom: 12px;
	}

	.upload-tip {
		margin-top: 10rpx;

		text {
			font-size: 24rpx;
			color: #999;
		}
	}

	.audit-fail-tip {
		display: flex;
		align-items: center;
		padding: 10px;
		background-color: #fef0f0;
		border-radius: 4px;
		color: #fa3534;
		margin: 0 0 12px 0;
		.tip-text {
			margin-left: 8px;
			font-size: 14px;
		}
	}

	.audit-pending-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.85);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
		.pending-box {
			display: flex;
			flex-direction: column;
			align-items: center;
			text-align: center;
		}
		.pending-text {
			margin-top: 15px;
			font-size: 16px;
			color: #606266;
		}
	}

	.button-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #ffffff;
		padding: 10px 15px;
		padding-bottom: calc(10px + constant(safe-area-inset-bottom));
		padding-bottom: calc(10px + env(safe-area-inset-bottom));
		border-top: 1px solid #f0f0f0;
		box-sizing: border-box;
		z-index: 99;
	}
	
	// 经纬度布局样式优化
	.location-container {
		display: flex;
		align-items: center;
		width: 100%;
		gap: 10px; // 使用gap替代margin
	}
	
	.location-inputs {
		display: flex;
		flex: 1; // 占据剩余空间
		gap: 8px; // 两个输入框之间的间距
		
		.location-input {
			flex: 1; // 让每个输入框平均分配空间
		}
	}
	
	.location-btn {
		flex-shrink: 0; // 防止按钮被压缩
		min-width: 60px; // 设置最小宽度
	}
	
	// 市场选择器样式
	.market-selector {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1px solid #DADBDE;
		min-height: 44rpx;

		.market-text {
			flex: 1;
			font-size: 28rpx;
			color: #303133;

			&.placeholder {
				color: #C0C4CC;
			}

			&.disabled {
				color: #C0C4CC;
			}
		}
	}



	// uView表单兼容性调整
	::v-deep .u-form-item__body {
		padding-bottom: 10px;
	}
</style>