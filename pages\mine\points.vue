<template>
	<scroll-view
		class="points-container"
		scroll-y="true"
		:refresher-enabled="true"
		:refresher-triggered="refreshing"
		@refresherrefresh="onRefresh"
	>
		<!-- 积分头部区域 -->
		<view class="points-header">
			<view class="points-card">
				<view class="points-info">
					<text class="points-label">我的积分</text>
					<text class="points-value">{{ userPoints }}</text>
				</view>
				<view class="points-actions">
					<view class="points-icon">
						<text class="icon">💎</text>
					</view>
					<view class="points-record-btn" @click="goToPointsRecord">
						<text class="record-text">积分记录</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 签到区域 -->
		<view class="checkin-section">
			<view class="section-title">
				<text class="title-text">每日签到</text>
				<text class="title-desc">坚持签到，获得积分奖励</text>
			</view>

			<!-- 本周签到记录 -->
			<view class="weekly-checkin-card">
				<view class="weekly-header">
					<text class="weekly-title">本周签到记录</text>
					<text class="weekly-desc">已签到 {{ getSignedDaysCount() }} 天</text>
				</view>
				<view class="weekly-calendar">
					<view
						v-for="record in weeklySignInRecords"
						:key="record.date"
						class="day-item"
						:class="{ 'signed': record.signedIn, 'today': isToday(record.date) }"
					>
						<text class="day-week">{{ record.dayOfWeek.replace('星期', '') }}</text>
						<text class="day-date">{{ formatDayDate(record.date) }}</text>
						<view class="day-status">
							<text class="status-icon">{{ record.signedIn ? '✅' : '⭕' }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 今日签到按钮 -->
			<view class="today-checkin-card" :class="{ 'checked': getTodaySignStatus() }">
				<view class="checkin-content">
					<view class="checkin-icon">
						<text class="icon">{{ getTodaySignStatus() ? '✅' : '📅' }}</text>
					</view>
					<view class="checkin-info">
						<text class="checkin-title">{{ getTodaySignStatus() ? '今日已签到' : '立即签到' }}</text>
						<text class="checkin-desc">{{ getTodaySignStatus() ? '明天再来签到吧' : '签到获得积分奖励' }}</text>
					</view>
				</view>
				<view v-if="!getTodaySignStatus()" class="checkin-btn" @click="handleCheckin">
					<text class="btn-text">签到</text>
				</view>
			</view>
		</view>

		<!-- 任务列表区域 -->
		<view class="tasks-section">
			<view class="section-title">
				<text class="title-text">积分任务</text>
				<text class="title-desc">完成任务获得更多积分</text>
			</view>
			<view class="tasks-list">
				<view 
					v-for="task in tasks" 
					:key="task.taskId" 
					class="task-item"
					:class="{ 'completed': task.completed }"
				>
					<view class="task-icon">
						<text class="icon">{{ getTaskIcon(task.taskId) }}</text>
					</view>
					<view class="task-info">
						<text class="task-name">{{ task.taskName }}</text>
						<text class="task-desc">{{ getTaskDesc(task) }}</text>
						<view v-if="task.pointsReward" class="task-reward">
							<text class="reward-text">奖励: {{ task.pointsReward }} 积分</text>
						</view>
					</view>
					<view class="task-status">
						<view v-if="task.completed" class="status-completed">
							<text class="status-text">已完成</text>
						</view>
						<view v-else-if="task.taskId === 2" class="status-share">
							<!-- 邀请好友使用分享按钮 -->
							<button class="share-btn" open-type="share" @click="handleTaskAction(task)">
								<text class="status-text">分享邀请</text>
							</button>
						</view>
						<view v-else class="status-pending" @click="handleTaskAction(task)">
							<text class="status-text">去完成</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</scroll-view>
</template>

<script>
import { getStatus, completeTask } from '@/api/buy/taskDefinition.js'

export default {
	data() {
		return {
			userPoints: 0,
			weeklySignInRecords: [], // 本周签到记录
			tasks: [],
			loading: false,
			refreshing: false
		}
	},
	onLoad() {
		this.checkLoginAndGetData()
	},
	onShow() {
		// 页面显示时刷新数据
		this.checkLoginAndGetData()
	},

	// 微信小程序分享
	onShareAppMessage: function (res) {
		if (res.from === 'button') {
			// 来自页面内转发按钮
			console.log('分享按钮被点击:', res.target)
		}

		// 获取当前用户ID
		const userId = this.$store.state.user?.userId || ''

		return {
			title: '容易购 - 五金建材行业专业平台',
			path: `/pages/login?inviteUserId=${userId}`
		}
	},
	methods: {
		// 检查登录状态并获取数据
		checkLoginAndGetData() {
			// 检查用户是否登录
			if (!this.$store.state.user || !this.$store.state.user.userId) {
				uni.showModal({
					title: '提示',
					content: '请先登录后查看积分信息',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login'
							})
						} else {
							uni.navigateBack()
						}
					}
				})
				return
			}
			this.getUserPointsStatus()
		},

		// 获取用户积分状态
		async getUserPointsStatus() {
			if (this.loading) return
			
			this.loading = true
			try {
				const response = await getStatus()
				if (response.code === 200) {
					const data = response.data
					this.userPoints = data.userPoints || 0
					this.weeklySignInRecords = data.weeklySignInRecords || []
					this.tasks = data.tasks || []
				} else {
					uni.showToast({
						title: response.msg || '获取积分信息失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('获取积分状态失败:', error)
				uni.showToast({
					title: '获取积分信息失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 下拉刷新
		async onRefresh() {
			this.refreshing = true
			try {
				await this.getUserPointsStatus()
			} finally {
				this.refreshing = false
			}
		},

		// 跳转到积分记录页面
		goToPointsRecord() {
			uni.navigateTo({
				url: '/pages/mine/pointsRecord'
			})
		},

		// 获取今日签到状态
		getTodaySignStatus() {
			const today = this.getLocalDateString() // 使用本地时间
			const todayRecord = this.weeklySignInRecords.find(record => record.date === today)
			return todayRecord ? todayRecord.signedIn : false
		},

		// 获取已签到天数
		getSignedDaysCount() {
			return this.weeklySignInRecords.filter(record => record.signedIn).length
		},

		// 判断是否是今天
		isToday(date) {
			const today = this.getLocalDateString()
			return date === today
		},

		// 获取本地日期字符串 (YYYY-MM-DD 格式)
		getLocalDateString() {
			const now = new Date()
			const year = now.getFullYear()
			const month = (now.getMonth() + 1).toString().padStart(2, '0')
			const day = now.getDate().toString().padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		// 格式化日期显示（只显示日）
		formatDayDate(date) {
			return new Date(date).getDate()
		},

		// 获取任务图标
		getTaskIcon(taskId) {
			const iconMap = {
				1: '📅', // 每日签到
				2: '👥', // 邀请好友
				3: '📝', // 发布易购圈
				4: '📱'  // 关注公众号
			}
			return iconMap[taskId] || '⭐'
		},

		// 获取任务描述
		getTaskDesc(task) {
			if (task.completed) {
				return `已完成 ${task.completionCount} 次`
			}

			const descMap = {
				1: '每日签到获得积分奖励',
				2: '分享给好友，好友注册成功获得奖励',
				3: '发布易购圈内容分享行业动态',
				4: '关注容易购微信公众号获取最新资讯'
			}
			return descMap[task.taskId] || task.description || '完成任务获得积分奖励'
		},

		// 处理签到（调用完成任务接口，taskId=1）
		async handleCheckin() {
			if (this.getTodaySignStatus()) {
				uni.showToast({
					title: '今日已签到',
					icon: 'none'
				})
				return
			}

			await this.completeTaskById(1)
		},

		// 完成指定任务
		async completeTaskById(taskId) {
			try {
				uni.showLoading({
					title: '处理中...'
				})

				const response = await completeTask(taskId)
				uni.hideLoading()

				if (response.code === 200) {
					// 显示任务完成信息，包含获得的积分
					uni.showToast({
						title: response.msg || '任务完成！',
						icon: 'success',
						duration: 2000
					})
					// 刷新页面数据
					setTimeout(() => {
						this.getUserPointsStatus()
					}, 500)
				} else {
					uni.showToast({
						title: response.msg || '任务完成失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('完成任务失败:', error)
				uni.showToast({
					title: '操作失败，请稍后重试',
					icon: 'none'
				})
			}
		},

		// 处理任务操作
		handleTaskAction(task) {
			if (task.completed) {
				return
			}

			switch (task.taskId) {
				case 1: // 每日签到
					this.handleCheckin()
					break
				case 2: // 邀请好友
					this.handleInviteFriend()
					break
				case 3: // 发布易购圈
					this.handlePublishPost()
					break
				case 4: // 关注公众号
					this.handleFollowWechat()
					break
				default:
					// 其他任务直接调用完成任务接口
					this.completeTaskById(task.taskId)
			}
		},

		// 邀请好友（使用微信分享）
		handleInviteFriend() {
			// 在微信小程序中，分享功能由 button 的 open-type="share" 触发
			// 这里只是提示用户点击分享按钮
			uni.showToast({
				title: '请点击分享按钮邀请好友',
				icon: 'none'
			})
		},

		// 发布易购圈
		handlePublishPost() {
			uni.navigateTo({
				url: '/pages/circle/publish/publish'
			})
		},

		// 关注公众号
		handleFollowWechat() {
			uni.showModal({
				title: '关注公众号',
				content: '请在微信中搜索"容易购"公众号并关注，关注后点击确定完成任务',
				confirmText: '已关注',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 用户确认已关注，完成任务
						this.completeTaskById(4)
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.points-container {
	height: 100vh;
	background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
	padding: 20rpx;
	box-sizing: border-box;
}

.points-header {
	margin-bottom: 30rpx;

	.points-card {
		background: linear-gradient(135deg, #DD1A21 0%, #ff4757 100%);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 8rpx 25rpx rgba(221, 26, 33, 0.3);

		.points-info {
			.points-label {
				display: block;
				color: rgba(255, 255, 255, 0.8);
				font-size: 28rpx;
				margin-bottom: 10rpx;
			}

			.points-value {
				display: block;
				color: #ffffff;
				font-size: 48rpx;
				font-weight: bold;
			}
		}

		.points-actions {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 16rpx;

			.points-icon {
				.icon {
					font-size: 60rpx;
				}
			}

			.points-record-btn {
				background: rgba(255, 255, 255, 0.2);
				border: 1rpx solid rgba(255, 255, 255, 0.3);
				border-radius: 20rpx;
				padding: 8rpx 20rpx;

				.record-text {
					color: #ffffff;
					font-size: 22rpx;
				}
			}
		}
	}
}

.checkin-section, .tasks-section {
	margin-bottom: 30rpx;

	.section-title {
		margin-bottom: 20rpx;

		.title-text {
			display: block;
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 8rpx;
		}

		.title-desc {
			display: block;
			font-size: 24rpx;
			color: #666;
		}
	}
}

// 本周签到记录卡片
.weekly-checkin-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

	.weekly-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		.weekly-title {
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
		}

		.weekly-desc {
			font-size: 24rpx;
			color: #DD1A21;
			background: #fef0f0;
			padding: 4rpx 12rpx;
			border-radius: 12rpx;
		}
	}

	.weekly-calendar {
		display: flex;
		justify-content: space-between;
		gap: 8rpx;

		.day-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 16rpx 8rpx;
			border-radius: 12rpx;
			background: #f8f9fa;
			border: 2rpx solid transparent;

			&.signed {
				background: linear-gradient(135deg, #fef0f0, #ffffff);
				border-color: #DD1A21;
			}

			&.today {
				background: linear-gradient(135deg, #e6f7ff, #ffffff);
				border-color: #1890ff;
			}

			&.signed.today {
				background: linear-gradient(135deg, #fef0f0, #e6f7ff);
				border-color: #DD1A21;
			}

			.day-week {
				font-size: 22rpx;
				color: #666;
				margin-bottom: 8rpx;
			}

			.day-date {
				font-size: 26rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}

			.day-status {
				.status-icon {
					font-size: 24rpx;
				}
			}
		}
	}
}

// 今日签到卡片
.today-checkin-card {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	border: 2rpx solid transparent;

	&.checked {
		border-color: #DD1A21;
		background: linear-gradient(135deg, #fef0f0, #ffffff);
	}

	.checkin-content {
		display: flex;
		align-items: center;

		.checkin-icon {
			margin-right: 20rpx;

			.icon {
				font-size: 40rpx;
			}
		}

		.checkin-info {
			.checkin-title {
				display: block;
				font-size: 28rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}

			.checkin-desc {
				display: block;
				font-size: 24rpx;
				color: #666;
			}
		}
	}

	.checkin-btn {
		background: linear-gradient(135deg, #DD1A21, #ff4757);
		border-radius: 25rpx;
		padding: 12rpx 30rpx;

		.btn-text {
			color: #ffffff;
			font-size: 26rpx;
			font-weight: 500;
		}
	}
}

.tasks-list {
	.task-item {
		background: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 16rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		border: 2rpx solid transparent;

		&.completed {
			border-color: #DD1A21;
			background: linear-gradient(135deg, #fef0f0, #ffffff);
		}

		.task-icon {
			margin-right: 20rpx;

			.icon {
				font-size: 36rpx;
			}
		}

		.task-info {
			flex: 1;

			.task-name {
				display: block;
				font-size: 28rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}

			.task-desc {
				display: block;
				font-size: 24rpx;
				color: #666;
				margin-bottom: 8rpx;
			}

			.task-reward {
				.reward-text {
					font-size: 22rpx;
					color: #DD1A21;
					background: #fef0f0;
					padding: 4rpx 12rpx;
					border-radius: 12rpx;
					display: inline-block;
				}
			}
		}

		.task-status {
			.status-completed {
				background: #fef0f0;
				border: 1rpx solid #ffccc7;
				border-radius: 20rpx;
				padding: 8rpx 20rpx;

				.status-text {
					color: #DD1A21;
					font-size: 24rpx;
				}
			}

			.status-pending {
				background: linear-gradient(135deg, #DD1A21, #ff4757);
				border-radius: 20rpx;
				padding: 8rpx 20rpx;

				.status-text {
					color: #ffffff;
					font-size: 24rpx;
				}
			}

			.status-share {
				.share-btn {
					background: linear-gradient(135deg, #DD1A21, #ff4757);
					border-radius: 20rpx;
					padding: 8rpx 20rpx;
					border: none;
					font-size: 24rpx;
					line-height: 1;

					.status-text {
						color: #ffffff;
						font-size: 24rpx;
					}

					&::after {
						border: none;
					}
				}
			}
		}
	}
}


</style>
