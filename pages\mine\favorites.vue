<template>
	<view class="favorites-container">
		<!-- 顶部tabs -->
		<view class="tabs-container">
			<view class="tabs-wrapper">
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'all' }"
					@click="switchTab('all')"
				>
					<text class="tab-text">全部</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'store' }"
					@click="switchTab('store')"
				>
					<text class="tab-text">商家</text>
				</view>
				<view 
					class="tab-item" 
					:class="{ active: activeTab === 'post' }"
					@click="switchTab('post')"
				>
					<text class="tab-text">论坛</text>
				</view>
			</view>
		</view>
		
		<!-- 收藏列表 -->
		<view class="content-container">
			<scroll-view class="favorites-scroll" scroll-y="true" @scrolltolower="loadMoreFavorites">
				<view class="favorites-list">
					<view
						v-for="favorite in favoriteList"
						:key="favorite.favoriteId"
						class="favorite-item"
						@click="goToDetail(favorite)"
					>
						<!-- 商家收藏 -->
						<view v-if="favorite.targetType === 'store' && favorite.storeInfo" class="store-favorite">
							<image
								:src="getImageUrl(favorite.storeInfo.storeAvatar)"
								class="store-avatar"
								mode="aspectFill"
							></image>
							<view class="store-info">
								<text class="store-name">{{ favorite.storeInfo.storeName }}</text>
								<text class="store-business">{{ getBusinessPreview(favorite.storeInfo.mainBusiness) }}</text>
								<view class="store-bottom-info">
									<view class="info-item">
										<text class="icon">📍</text>
										<text class="info-text">{{ favorite.storeInfo.marketAddress }}</text>
									</view>
									<view class="info-item">
										<text class="icon">👤</text>
										<text class="info-text">{{ favorite.storeInfo.contactPerson }}</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 帖子收藏 -->
						<view v-if="favorite.targetType === 'post' && favorite.postInfo" class="post-favorite">
							<view class="post-info">
								<text class="post-content">{{ getContentPreview(favorite.postInfo.postContent) }}</text>
								<text class="post-author">{{ favorite.postInfo.authorNickname }}</text>
								<text class="favorite-time">收藏于 {{ formatTime(favorite.createTime) }}</text>
							</view>
							<image
								:src="getImageUrl(favorite.postInfo.authorAvatar)"
								class="post-avatar"
								mode="aspectFill"
							></image>
						</view>
					</view>
					
					<!-- 加载更多 -->
					<view v-if="hasMore" class="load-more">
						<text>加载更多...</text>
					</view>
					<view v-else-if="favoriteList.length > 0" class="no-more">
						<text>没有更多了</text>
					</view>
					<view v-else class="empty-favorites">
						<text>暂无收藏</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { listFavorite } from '@/api/buy/favorite'
import config from '@/config'
import eventBus from '@/utils/eventBus'

export default {
	data() {
		return {
			activeTab: 'all', // 当前选中的tab
			favoriteList: [], // 收藏列表
			pageNum: 1, // 当前页码
			pageSize: 20, // 每页数量
			hasMore: true, // 是否还有更多数据
			loading: false, // 是否正在加载
			baseUrl: config.baseUrl
		}
	},
	onLoad() {
		this.getFavoriteList();
		// 监听收藏状态变化事件
		eventBus.on('favoriteChanged', this.handleFavoriteChanged);
	},

	onUnload() {
		// 页面卸载时移除事件监听
		eventBus.off('favoriteChanged', this.handleFavoriteChanged);
	},
	methods: {
		// 切换tab
		switchTab(tab) {
			this.activeTab = tab;
			this.favoriteList = [];
			this.pageNum = 1;
			this.hasMore = true;
			this.getFavoriteList();
		},

		// 刷新收藏列表
		refreshFavoriteList() {
			// 重置列表状态
			this.favoriteList = [];
			this.pageNum = 1;
			this.hasMore = true;
			// 重新获取数据
			this.getFavoriteList();
		},

		// 处理收藏状态变化
		handleFavoriteChanged(data) {
			const { action, targetType, targetId } = data;

			if (action === 'remove') {
				// 从列表中移除对应的收藏项
				this.favoriteList = this.favoriteList.filter(item => {
					return !(item.targetType === targetType && item.targetId == targetId);
				});

				console.log(`收藏已移除: ${targetType} ${targetId}`);
			} else if (action === 'add') {
				// 如果是添加收藏，且当前tab匹配，则刷新列表
				if (this.activeTab === 'all' || this.activeTab === targetType) {
					this.refreshFavoriteList();
				}

				console.log(`收藏已添加: ${targetType} ${targetId}`);
			}
		},
		
		// 获取收藏列表
		async getFavoriteList(loadMore = false) {
			if (this.loading) return;
			if (!this.$store.state.user || !this.$store.state.user.userId) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				return;
			}
			
			this.loading = true;
			
			try {
				const params = {
					userId: this.$store.state.user.userId,
					pageNum: loadMore ? this.pageNum : 1,
					pageSize: this.pageSize
				};
				
				// 根据tab添加筛选条件
				if (this.activeTab !== 'all') {
					params.targetType = this.activeTab;
				}
				
				const response = await listFavorite(params);
				if (response.code === 200) {
					const newFavorites = response.rows || [];
					
					if (loadMore) {
						this.favoriteList = [...this.favoriteList, ...newFavorites];
					} else {
						this.favoriteList = newFavorites;
						this.pageNum = 1;
					}
					
					// 判断是否还有更多数据
					this.hasMore = newFavorites.length === this.pageSize;
					if (loadMore && newFavorites.length > 0) {
						this.pageNum++;
					}
				}
			} catch (error) {
				console.error('获取收藏列表失败:', error);
				uni.showToast({
					title: '获取收藏列表失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 加载更多收藏
		loadMoreFavorites() {
			if (this.hasMore && !this.loading) {
				this.getFavoriteList(true);
			}
		},
		
		// 跳转到详情页
		goToDetail(favorite) {
			if (favorite.targetType === 'store') {
				uni.navigateTo({
					url: `/pages/storeDetail/storeDetail?id=${favorite.targetId}`
				});
			} else if (favorite.targetType === 'post') {
				uni.navigateTo({
					url: `/pages/circle/detail/detail?postId=${favorite.targetId}`
				});
			}
		},
		
		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '/static/images/profile.jpg';
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			return this.baseUrl + imagePath;
		},
		
		// 获取主营业务预览
		getBusinessPreview(business) {
			if (!business) return '';
			return business.length > 30 ? business.substring(0, 30) + '...' : business;
		},
		
		// 获取内容预览
		getContentPreview(content) {
			if (!content) return '';
			return content.length > 60 ? content.substring(0, 60) + '...' : content;
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return '';
			const date = new Date(time);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
		}
	}
}
</script>

<style lang="scss">
.favorites-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.tabs-container {
	background-color: #fff;
	border-bottom: 1px solid #e5e5e5;
	
	.tabs-wrapper {
		display: flex;
		align-items: center;
		
		.tab-item {
			flex: 1;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			
			.tab-text {
				font-size: 28rpx;
				color: #666;
				transition: color 0.3s;
			}
			
			&.active {
				.tab-text {
					color: #2979ff;
					font-weight: 500;
				}
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 4rpx;
					background-color: #2979ff;
					border-radius: 2rpx;
				}
			}
		}
	}
}

.content-container {
	flex: 1;
	
	.favorites-scroll {
		height: calc(100vh - 88rpx);
		
		.favorites-list {
			padding: 20rpx;
			
			.favorite-item {
				background-color: #fff;
				border-radius: 12rpx;
				margin-bottom: 20rpx;
				overflow: hidden;

				// 商家收藏样式 - 图片在左边
				.store-favorite {
					display: flex;
					padding: 24rpx;
					align-items: flex-start;

					.store-avatar {
						width: 120rpx;
						height: 120rpx;
						border-radius: 12rpx;
						margin-right: 20rpx;
						flex-shrink: 0;
					}

					.store-info {
						flex: 1;

						.store-name {
							font-size: 32rpx;
							font-weight: bold; // 商家名称加粗
							color: #ff6b6b; // 商家名称用红色
							margin-bottom: 12rpx;
							display: block;
						}

						.store-business {
							font-size: 26rpx;
							color: #666;
							line-height: 1.4;
							margin-bottom: 12rpx;
							display: block;
						}

						.store-bottom-info {
							display: flex;
							flex-direction: column;
							gap: 8rpx;

							.info-item {
								display: flex;
								align-items: center;

								.icon {
									font-size: 24rpx;
									margin-right: 8rpx;
									width: 24rpx;
									text-align: center;
								}

								.info-text {
									font-size: 24rpx;
									color: #999;
									flex: 1;
								}
							}
						}
					}
				}

				// 帖子收藏样式 - 图片在右边
				.post-favorite {
					display: flex;
					padding: 24rpx;
					align-items: flex-start;

					.post-info {
						flex: 1;
						margin-right: 20rpx;

						.post-content {
							font-size: 28rpx;
							font-weight: bold; // 帖子内容加粗
							color: #000; // 帖子内容用黑色，更突出
							line-height: 1.4;
							margin-bottom: 12rpx;
							display: block;
						}

						.post-author {
							font-size: 26rpx;
							font-weight: 500;
							color: #2979ff; // 作者名称用蓝色
							margin-bottom: 8rpx;
							display: block;
						}

						.favorite-time {
							font-size: 24rpx;
							color: #999;
							margin-bottom: 4rpx;
							display: block;
						}
					}

					.post-avatar {
						width: 120rpx;
						height: 120rpx;
						border-radius: 12rpx;
						flex-shrink: 0;
					}
				}
			}
			
			.load-more, .no-more, .empty-favorites {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;
				
				text {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
}
</style>
