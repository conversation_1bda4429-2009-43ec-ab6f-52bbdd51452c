<template>
	<view class="page-container">
		<!-- 店铺存在时的主体内容 -->
		<template v-if="storeInfo">
			<!-- 顶部店铺信息卡片 -->
			<view class="header-section">
				<!-- 店铺名称和地址 -->
				<view class="store-title-box">
					<view class="title-content">
						<text class="store-name">{{ storeInfo.storeName || '我的店铺' }}</text>
						<view class="market-tag">
							<u-icon name="map-fill" size="12" color="#fff" style="margin-right: 4px;"></u-icon>
							<text class="store-market">{{ storeInfo.marketAddress || '掌上五金建材市场' }}</text>
						</view>
					</view>
					<view class="store-status" :class="getStatusClass">
						<text class="status-text">{{ getStatusText }}</text>
					</view>
				</view>

				<!-- 店铺详细信息 (已重构) -->
				<view class="store-info-box">
					<!-- 主要信息行：头像、联系方式、操作按钮 -->
					<view class="info-primary-row">
						<view class="avatar-container">
							<u-avatar :src="getAvatarUrl(storeInfo.avatar)" size="60" shape="square"></u-avatar>
							<view v-if="getStatusClass === 'status-approved'" class="avatar-badge">
								<u-icon name="checkmark" size="12" color="#fff"></u-icon>
							</view>
						</view>
						<view class="contact-info">
							<text class="contact-name">{{ storeInfo.contactPerson }}</text>
							<view class="phone-container">
								<u-icon name="phone-fill" size="14" color="rgba(255,255,255,0.8)" style="margin-right: 4px;"></u-icon>
								<text class="phone">{{ storeInfo.phone }}</text>
							</view>
						</view>
						<view class="action-buttons">
							<view class="detail-button" @click="goTo('detail')">
								<text class="button-text">店铺详情</text>
							</view>
						</view>
					</view>
					<!-- 统计数据行 -->
					<view class="stats-line">
						<view class="stat-item">
							<text class="stat-number">{{ storeInfo.visitorCount || 0 }}</text>
							<text class="stat-label">访客量</text>
						</view>
						<view class="stat-divider"></view>
						<view class="stat-item">
							<text class="stat-number">{{ storeInfo.popularityScore || 0 }}</text>
							<text class="stat-label">人气值</text>
						</view>
						<view class="stat-divider"></view>
						<view class="stat-item">
							<text class="stat-number">¥{{ storeInfo.money || '0.00' }}</text>
							<text class="stat-label">店铺余额</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 白色卡片区域 -->
			<view class="content-wrapper">
				<!-- 管理功能按钮 -->
				<view class="management-buttons">
					<view class="button-item" @click="goToProductManagement">
						<text class="button-icon">📦</text>
						<text class="button-text">商品管理</text>
					</view>
					<view class="button-item" @click="goToStoreManagement">
						<text class="button-icon">🏪</text>
						<text class="button-text">店铺管理</text>
					</view>
					<view class="button-item" @click="goToRecharge">
						<text class="button-icon">💰</text>
						<text class="button-text">余额充值</text>
					</view>
				</view>
				
				<!-- 店铺状态详情卡片 -->
				<view class="info-card">
					<view class="status-header">
						<u-icon :name="getStatusDetailIcon" :color="getStatusDetailIconColor" size="20"></u-icon>
						<text class="status-title">{{ getStatusDetailTitle }}</text>
						<view class="status-badge" :class="getStatusClass">
							<text class="badge-text">{{ getStatusText }}</text>
						</view>
					</view>
					<view class="status-content">
						<text class="status-desc">{{ getStatusDetailDesc }}</text>
						<view class="status-info-grid">
							<view class="info-item">
								<text class="info-label">创建时间</text>
								<text class="info-value">{{ formatDate(storeInfo.createTime) }}</text>
							</view>
							<view class="info-item" v-if="storeInfo.auditTime">
								<text class="info-label">审核时间</text>
								<text class="info-value">{{ formatDate(storeInfo.auditTime) }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">店铺编号</text>
								<text class="info-value">{{ storeInfo.storeId || '暂无' }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">访客展示</text>
								<text class="info-value">{{ storeInfo.showVisitorInfo === '1' ? '已开启' : '已关闭' }}</text>
							</view>
						</view>
						<view v-if="storeInfo.auditStatus === '2' && storeInfo.auditRemark" class="audit-remark">
							<text class="remark-label">审核意见：</text>
							<text class="remark-text">{{ storeInfo.auditRemark }}</text>
						</view>
					</view>
				</view>


				<!-- 访客信息 -->
				<view class="info-card visitor-section">
					<view class="section-title">
						<text class="title-text">访客信息</text>
						<view class="switch-box">
							<text>访客列表展示</text>
							<u-switch :value="showVisitorInfo" @change="handleSwitchChange" size="20" activeColor="#19be6b"></u-switch>
						</view>
					</view>
					<!-- 自定义Tabs -->
					<view class="custom-tabs">
						<view
							class="tab-item"
							:class="{ 'active': currentTab === index }"
							v-for="(tab, index) in visitorTabs"
							:key="index"
							@click="switchTab(index)"
						>
							<text class="tab-text">{{ tab.name }}</text>
						</view>
					</view>
					<view class="visitor-content">
						<!-- 访客记录 -->
						<view v-show="currentTab === 0">
							<view class="visitor-list" v-if="visitorList.length > 0">
								<view class="visitor-item" v-for="visitor in visitorList" :key="visitor.id">
									<view class="visitor-avatar">
										<image
											:src="getAvatarUrl(visitor.avatar)"
											mode="aspectFill"
											@error="handleImageError"
										></image>
									</view>
									<view class="visitor-info">
										<text class="visitor-name">{{ visitor.nickName || '匿名用户' }}</text>
										<text class="visitor-time">{{ formatVisitTime(visitor.createTime) }}</text>
									</view>
								</view>
							</view>
							<view class="empty-state" v-else-if="!loadingVisitors">
								<u-empty
									mode="list"
									text="暂无访客记录"
									icon="http://cdn.uviewui.com/uview/empty/data.png"
									marginTop="20"
								></u-empty>
							</view>
							<view class="loading-state" v-if="loadingVisitors">
								<u-loading-icon mode="flower" text="加载中..." textSize="14"></u-loading-icon>
							</view>
						</view>

						<!-- 收藏记录 -->
						<view v-show="currentTab === 1">
							<view class="favorite-list" v-if="favoriteList.length > 0">
								<view class="favorite-item" v-for="favorite in favoriteList" :key="favorite.favoriteId">
									<view class="favorite-avatar">
										<image
											:src="getAvatarUrl(favorite.avatar)"
											mode="aspectFill"
											@error="handleImageError"
										></image>
									</view>
									<view class="favorite-info">
										<text class="favorite-name">{{ favorite.nickName || '匿名用户' }}</text>
										<text class="favorite-time">{{ formatVisitTime(favorite.createTime) }}</text>
									</view>
								</view>
							</view>
							<view class="empty-state" v-else-if="!loadingFavorites">
								<u-empty
									mode="list"
									text="暂无收藏记录"
									icon="http://cdn.uviewui.com/uview/empty/data.png"
									marginTop="20"
								></u-empty>
							</view>
							<view class="loading-state" v-if="loadingFavorites">
								<u-loading-icon mode="flower" text="加载中..." textSize="14"></u-loading-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
		</template>

		<!-- 店铺不存在时的提示 -->
		<view v-else class="empty-store-container">
			<u-empty
				mode="car"
				text="您还没有自己的店铺哦"
				icon="http://cdn.uviewui.com/uview/empty/car.png"
			>
				<u-button
					type="primary"
					text="立即入驻"
					customStyle="margin-top: 20px"
					@click="goTo('create')"
				></u-button>
			</u-empty>
		</view>
	</view>
</template>

<script>
	import { listStoreInfo, updateStoreInfo } from "@/api/buy/storeInfo.js"
	import { listSeeLog } from "@/api/buy/seeLog.js"
	import { favoriteLog } from "@/api/buy/favorite.js"
	import { baseUrl } from "@/config";

	export default {
		data() {
			return {
				userId: this.$store.state.user.userId,
				storeInfo: null, // 存储店铺信息
				showVisitorInfo: true, // UI开关绑定的布尔值
				visitorTabs: [
					{ name: '访客记录' },
					{ name: '收藏记录' }
				],
				currentTab: 0,
				visitorList: [], // 访客记录列表
				loadingVisitors: false, // 访客记录加载状态
				favoriteList: [], // 收藏记录列表
				loadingFavorites: false, // 收藏记录加载状态
				baseUrl
			}
		},
		computed: {
			// 获取审核状态文本
			getStatusText() {
				if (!this.storeInfo) return '未知状态';
				const status = this.storeInfo.auditStatus;
				switch(String(status)) {
					case '0': return '审核中';
					case '1': return '营业中';
					case '2': return '未通过';
					default: return '未知状态';
				}
			},
			// 获取状态样式类
			getStatusClass() {
				if (!this.storeInfo) return 'status-unknown';
				switch(String(this.storeInfo.auditStatus)) {
					case '0': return 'status-pending';
					case '1': return 'status-approved';
					case '2': return 'status-rejected';
					default: return 'status-unknown';
				}
			},
			// 获取状态详情图标
			getStatusDetailIcon() {
				if (!this.storeInfo) return 'info-circle';
				switch(String(this.storeInfo.auditStatus)) {
					case '0': return 'clock-fill';
					case '1': return 'checkmark-circle-fill';
					case '2': return 'close-circle-fill';
					default: return 'info-circle';
				}
			},
			// 获取状态详情图标颜色
			getStatusDetailIconColor() {
				if (!this.storeInfo) return '#909399';
				switch(String(this.storeInfo.auditStatus)) {
					case '0': return '#ff9500';
					case '1': return '#19be6b';
					case '2': return '#fa3534';
					default: return '#909399';
				}
			},
			// 获取状态详情标题
			getStatusDetailTitle() {
				if (!this.storeInfo) return '店铺状态';
				switch(String(this.storeInfo.auditStatus)) {
					case '0': return '店铺审核中';
					case '1': return '店铺正常营业';
					case '2': return '店铺审核未通过';
					default: return '店铺状态异常';
				}
			},
			// 获取状态详情描述
			getStatusDetailDesc() {
				if (!this.storeInfo) return '';
				switch(String(this.storeInfo.auditStatus)) {
					case '0': return '您的店铺申请正在审核中，审核通过后即可正常营业，请耐心等待。';
					case '1': return '恭喜！您的店铺已通过审核，现在可以正常营业，快去添加商品吧！';
					case '2': return '很遗憾，您的店铺申请未通过审核，请根据审核意见修改后重新提交。';
					default: return '店铺状态异常，请联系客服处理。';
				}
			},
		},
		onShow() {
			this.getStoreInfo();
		},
		methods: {
			formatDate(dateStr) {
				if (!dateStr) return '暂无';
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},

			getStoreInfo() {
				this.$modal.loading('加载中...');
				listStoreInfo({ userId: this.userId, pageNum: 1, pageSize: 1 }).then(res => {
					this.$modal.closeLoading();
					if (res.code === 200 && res.rows.length > 0) {
						this.storeInfo = res.rows[0];
						this.showVisitorInfo = this.storeInfo.showVisitorInfo === '1';
						this.getVisitorList();
						this.getFavoriteList();
					} else {
						this.storeInfo = null;
					}
				}).catch(() => {
					this.$modal.closeLoading();
					this.$modal.msgError("数据加载失败");
				});
			},

			async getVisitorList() {
				if (!this.storeInfo || !this.storeInfo.storeId) return;
				this.loadingVisitors = true;
				try {
					const params = { type: '0', targetId: this.storeInfo.storeId, pageNum: 1, pageSize: 20 };
					const res = await listSeeLog(params);
					if (res.code === 200) {
						this.visitorList = res.rows || [];
					}
				} catch (error) {
					console.error('获取访客记录失败:', error);
				} finally {
					this.loadingVisitors = false;
				}
			},

			async getFavoriteList() {
				if (!this.storeInfo || !this.storeInfo.storeId) return;
				this.loadingFavorites = true;
				try {
					const res = await favoriteLog(this.storeInfo.storeId);
					if (res.code === 200) {
						this.favoriteList = (res.rows || []).slice(0, 20); // Show up to 20
					}
				} catch (error) {
					console.error('获取收藏记录失败:', error);
				} finally {
					this.loadingFavorites = false;
				}
			},

			// 获取头像URL，统一处理默认头像
			getAvatarUrl(avatar) {
				if (!avatar || avatar === null || avatar === 'null' || avatar === '') {
					return '/static/images/profile.jpg'; // 使用统一的默认头像
				}
				if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
					return avatar;
				}
				return this.baseUrl + avatar;
			},

			// 处理图片加载错误
			handleImageError(e) {
				e.target.src = '/static/images/profile.jpg';
			},

			formatVisitTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const now = new Date();
				const diff = now.getTime() - date.getTime();
				const minutes = Math.floor(diff / 60000);
				const hours = Math.floor(diff / 3600000);
				const days = Math.floor(diff / 86400000);

				if (minutes < 1) return '刚刚';
				if (minutes < 60) return `${minutes}分钟前`;
				if (hours < 24) return `${hours}小时前`;
				return `${days}天前`;
			},
			
			handleSwitchChange(newValue) {
				if (!this.storeInfo) {
					this.$modal.msgError('店铺信息不存在');
					return;
				}
				const backendValue = newValue ? '1' : '0';

				const updateData = {
					storeId: this.storeInfo.storeId,
					showVisitorInfo: backendValue
				};
			
				this.$modal.loading('更新中...');
				updateStoreInfo(updateData).then(res => {
					this.$modal.closeLoading();
					if (res.code === 200) {
						this.showVisitorInfo = newValue;
						this.storeInfo.showVisitorInfo = backendValue;
						this.$modal.msgSuccess(newValue ? '已开启展示' : '已关闭展示');
					} else {
						this.$modal.msgError(res.msg || '更新失败');
					}
				}).catch(error => {
					this.$modal.closeLoading();
					this.$modal.msgError('更新失败');
					console.error('更新访客展示状态失败:', error);
				});
			},

			goTo(pageKey) {
				let url = '';
				switch(pageKey) {
					case 'detail':
						url = `/pages/storeDetail/storeDetail?id=${this.storeInfo.storeId}`;
						break;
					case 'edit':
					case 'create':
						url = `/pages/mine/storeInfo/storeInfo`;
						break;
				}
				if (url) uni.navigateTo({ url });
			},

			switchTab(index) {
				this.currentTab = index;
			},

			goToProductManagement() {
				uni.navigateTo({ url: '/pages/store/products/index' });
			},

			goToStoreManagement() {
				uni.navigateTo({ url: '/pages/mine/storeInfo/storeInfo' });
			},

			goToRecharge() {
				if (!this.storeInfo || !this.storeInfo.storeId) {
					this.$modal.msgError('店铺信息不存在');
					return;
				}
				uni.navigateTo({ url: `/pages/mine/recharge/recharge?storeId=${this.storeInfo.storeId}` });
			}
		}
	}
</script>

<style lang="scss">
// 美化后的样式
.page-container {
	background-color: #f7f8fa;
	min-height: 100vh;
}

.header-section {
	background: linear-gradient(135deg, #7b68ee 0%, #5d41a7 100%);
	padding: 50rpx 40rpx 60rpx; // 调整底部padding
	color: #ffffff;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: radial-gradient(circle, rgba(255,255,255,0.1), rgba(255,255,255,0) 60%);
		animation: rotate 20s linear infinite;
	}
	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}
}

.store-title-box {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 50rpx;
	position: relative;
	z-index: 1;

	.title-content { flex: 1; padding-right: 20rpx; }
	.store-name { font-size: 48rpx; font-weight: bold; margin-bottom: 16rpx; display: block; }
	.market-tag { display: inline-flex; align-items: center; background: rgba(255, 255, 255, 0.15); padding: 8rpx 20rpx; border-radius: 24rpx; }
	.store-market { font-size: 24rpx; opacity: 0.9; }
	.store-status {
		padding: 12rpx 24rpx; border-radius: 30rpx; flex-shrink: 0;
		.status-text { font-size: 24rpx; font-weight: 500; }
		&.status-approved { background: rgba(25, 190, 107, 0.8); }
		&.status-pending { background: rgba(255, 149, 0, 0.8); }
		&.status-rejected { background: rgba(250, 53, 52, 0.8); }
		&.status-unknown { background: rgba(144, 147, 153, 0.8); }
	}
}

// 重构后的店铺信息盒子
.store-info-box {
	display: flex;
	flex-direction: column;
	gap: 40rpx;
	position: relative;
	z-index: 1;

	.info-primary-row {
		display: flex;
		align-items: center;
		width: 100%;
	}

	.avatar-container {
		position: relative;
		margin-right: 24rpx;
		flex-shrink: 0;

		.avatar-badge {
			position: absolute;
			bottom: -4rpx;
			right: -4rpx;
			width: 36rpx;
			height: 36rpx;
			background: #19be6b;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 4rpx solid #5d41a7; // 边框色与背景色协调
		}
	}

	.contact-info {
		flex: 1;
		min-width: 0; // 防止 flex 元素溢出

		.contact-name {
			font-size: 40rpx;
			font-weight: bold;
			display: block;
			margin-bottom: 8rpx;
		}
		.phone-container {
			display: flex;
			align-items: center;
		}
		.phone {
			font-size: 28rpx;
			opacity: 0.9;
		}
	}

	.action-buttons {
		margin-left: 20rpx;
		.detail-button {
			background: #fff;
			border-radius: 40rpx;
			padding: 16rpx 32rpx;
			box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
			transition: all 0.2s ease;

			.button-text {
				color: #5d41a7;
				font-size: 26rpx;
				font-weight: bold;
			}

			&:active {
				transform: scale(0.95);
				opacity: 0.9;
			}
		}
	}

	.stats-line {
		display: flex;
		align-items: center;
		justify-content: center; // 将统计项作为一个整体居中
		background: rgba(0, 0, 0, 0.15); // 半透明背景，增加层次感
		border-radius: 16rpx;
		padding: 24rpx 0;
		width: 100%;
		box-sizing: border-box;

		.stat-item {
			text-align: center;
			padding: 0 10rpx;

			.stat-number {
				font-size: 36rpx;
				font-weight: bold;
				display: block;
				margin-bottom: 4rpx;
			}
			.stat-label {
				font-size: 24rpx;
				opacity: 0.8;
				display: block;
			}
		}

		.stat-divider {
			width: 2rpx;
			height: 60rpx;
			background: rgba(255, 255, 255, 0.25);
			margin: 0 50rpx; // 调整分隔符间距
		}
	}
}

.content-wrapper {
	padding: 0 30rpx; position: relative; margin-top: 24rpx; z-index: 2;
}

.info-card {
	background-color: #ffffff; border-radius: 16rpx; margin-bottom: 24rpx;
	padding: 30rpx; box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.status-header {
	display: flex; align-items: center; margin-bottom: 20rpx;
	.status-title { font-size: 32rpx; font-weight: 600; color: #303133; margin-left: 12rpx; flex: 1; }
	.status-badge {
		padding: 8rpx 16rpx; border-radius: 20rpx; font-size: 24rpx;
		.badge-text { font-weight: 500; }
		&.status-pending { background-color: rgba(255, 149, 0, 0.1); .badge-text { color: #ff9500; } }
		&.status-approved { background-color: rgba(25, 190, 107, 0.1); .badge-text { color: #19be6b; } }
		&.status-rejected { background-color: rgba(250, 53, 52, 0.1); .badge-text { color: #fa3534; } }
	}
}

.status-content {
	.status-desc { display: block; font-size: 28rpx; color: #606266; line-height: 1.6; margin-bottom: 24rpx; }
	.status-info-grid {
		display: grid; grid-template-columns: 1fr 1fr; gap: 30rpx; margin-bottom: 20rpx;
		.info-item {
			.info-label { display: block; font-size: 24rpx; color: #909399; margin-bottom: 8rpx; }
			.info-value { display: block; font-size: 26rpx; color: #303133; font-weight: 500; }
		}
	}
	.audit-remark {
		background-color: #fef0f0; border-left: 6rpx solid #f56c6c; border-radius: 8rpx;
		padding: 20rpx; margin-top: 20rpx;
		.remark-label { font-size: 26rpx; color: #f56c6c; font-weight: 500; }
		.remark-text { font-size: 26rpx; color: #606266; line-height: 1.5; }
	}
}

.management-buttons {
	display: flex; gap: 20rpx; margin-bottom: 24rpx;
	.button-item {
		flex: 1; display: flex; flex-direction: column; align-items: center; justify-content: center;
		padding: 30rpx 20rpx; border-radius: 16rpx; transition: all 0.3s ease;
		position: relative; overflow: hidden;
		&:active { transform: translateY(2rpx); }
		&:first-child { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3); }
		&:nth-child(2) { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); box-shadow: 0 8rpx 20rpx rgba(245, 87, 108, 0.3); }
		&:nth-child(3) { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); box-shadow: 0 8rpx 20rpx rgba(252, 182, 159, 0.3); }
		.button-icon { font-size: 48rpx; margin-bottom: 12rpx; filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2)); }
		.button-text { font-size: 28rpx; color: #fff; font-weight: 600; text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2); }
	}
}

.visitor-section {
	.section-title {
		display: flex; justify-content: space-between; align-items: center; margin-bottom: 30rpx;
		.title-text { font-size: 32rpx; font-weight: bold; color: #303133; }
		.switch-box { display: flex; align-items: center; font-size: 26rpx; color: #606266; gap: 16rpx; }
	}
	.custom-tabs {
		display: flex; margin-bottom: 30rpx; border-bottom: 1rpx solid #eee;
		.tab-item {
			flex: 1; text-align: center; padding-bottom: 20rpx; position: relative;
			.tab-text { font-size: 28rpx; color: #666; font-weight: 500; transition: all 0.3s; }
			&.active {
				.tab-text { color: #5d41a7; font-weight: 600; }
				&::after {
					content: ''; position: absolute; bottom: -1rpx; left: 50%;
					transform: translateX(-50%); width: 60rpx; height: 6rpx;
					background-color: #5d41a7; border-radius: 3rpx;
				}
			}
		}
	}
	.visitor-list, .favorite-list {
		display: grid; grid-template-columns: 1fr 1fr; gap: 20rpx;
	}
	.visitor-item, .favorite-item {
		display: flex; align-items: center; padding: 15rpx; background: #f8f9fa; border-radius: 12rpx; min-width: 0;
		.visitor-avatar, .favorite-avatar {
			width: 70rpx; height: 70rpx; border-radius: 50%; overflow: hidden; margin-right: 15rpx;
			flex-shrink: 0; background-color: #f0f0f0;
			image { width: 100%; height: 100%; }
		}
		.visitor-info, .favorite-info {
			flex: 1; min-width: 0;
			.visitor-name, .favorite-name {
				display: block; font-size: 26rpx; color: #333; font-weight: 500; margin-bottom: 6rpx;
				overflow: hidden; text-overflow: ellipsis; white-space: nowrap;
			}
			.visitor-time, .favorite-time { display: block; font-size: 22rpx; color: #999; }
		}
	}
	.empty-state, .loading-state { padding: 40rpx 0; text-align: center; }
}

.empty-store-container {
	padding-top: 200rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	height: calc(100vh - 200rpx);
}
</style>