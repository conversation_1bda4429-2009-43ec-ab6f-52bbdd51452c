import request from '@/utils/request'

// 查询层级节点（城市、市场、分类）列表
export function listNodes(query) {
  return request({
    url: '/buy/nodes/list',
    method: 'get',
    params: query
  })
}

// 查询层级节点（城市、市场、分类）详细
export function getNodes(id) {
  return request({
    url: '/buy/nodes/' + id,
    method: 'get'
  })
}

// 新增层级节点（城市、市场、分类）
export function addNodes(data) {
  return request({
    url: '/buy/nodes',
    method: 'post',
    data: data
  })
}

// 修改层级节点（城市、市场、分类）
export function updateNodes(data) {
  return request({
    url: '/buy/nodes',
    method: 'put',
    data: data
  })
}

// 删除层级节点（城市、市场、分类）
export function delNodes(id) {
  return request({
    url: '/buy/nodes/' + id,
    method: 'delete'
  })
}
