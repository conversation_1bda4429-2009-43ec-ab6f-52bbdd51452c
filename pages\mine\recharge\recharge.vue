<template>
	<view class="page-container">
		<!-- 顶部导航 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">余额充值</text>
				<view class="balance-info">
					<text class="balance-label">当前余额</text>
					<view class="balance-amount-wrapper">
						<text class="currency-symbol">¥</text>
						<text class="balance-amount" v-if="!loadingBalance">{{ currentBalance }}</text>
						<u-loading-icon v-else mode="circle" color="#fff" size="24"></u-loading-icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 主体卡片 -->
			<view class="main-card">
				<!-- 自定义Tabs -->
				<view class="tabs-container">
					<view class="custom-tabs">
						<view
							class="tab-item"
							:class="{ 'active': currentTab === index }"
							v-for="(tab, index) in tabs"
							:key="index"
							@click="switchTab(index)"
						>
							<text class="tab-text">{{ tab.name }}</text>
						</view>
					</view>
				</view>

				<!-- Tab内容 -->
				<view class="tab-content">
					<!-- 余额充值 -->
					<view v-if="currentTab === 0" class="recharge-content">
						<!-- 快捷金额选择 -->
						<view class="amount-section">
							<text class="section-title">选择充值金额</text>
							<view class="amount-grid">
								<view
									class="amount-item"
									:class="{ 'active': selectedAmount === amount }"
									v-for="amount in quickAmounts"
									:key="amount"
									@click="selectAmount(amount)"
								>
									<text class="amount-text">¥{{ amount }}</text>
								</view>
							</view>
						</view>

						<!-- 自定义金额输入 -->
						<view class="custom-amount-section">
							<text class="section-title">或 自定义金额</text>
							<view class="input-wrapper">
								<text class="input-currency-symbol">¥</text>
								<input
									class="amount-input"
									type="digit"
									placeholder="请输入充值金额"
									v-model="customAmount"
									@input="onCustomAmountInput"
								/>
							</view>
							<text class="amount-tip">单次最高可充值 ¥50,000</text>
						</view>

						<!-- 充值按钮 -->
						<view class="recharge-button-section">
							<button class="recharge-button" :disabled="!canRecharge" @click="handleRecharge">
								<text class="button-text">微信支付 ¥{{ finalAmount }}</text>
							</button>
						</view>
					</view>

					<!-- 充值记录 -->
					<view v-if="currentTab === 1" class="record-content">
						<scroll-view scroll-y style="height: 100%;">
							<view class="record-list" v-if="rechargeRecords.length > 0">
								<view class="record-item" v-for="(record, index) in processedRechargeRecords" :key="index">
									<view class="record-main-info">
										<view class="record-left">
											<text class="record-type">余额充值</text>
											<text class="record-time">{{ formatTime(record.createTime) }}</text>
										</view>
										<view class="record-right">
											<text class="record-amount">+{{ record.rechargeAmount }}</text>
											<text class="record-status" :class="record.statusClass">{{ record.statusText }}</text>
										</view>
									</view>
									<!-- 待支付状态显示支付按钮 -->
									<view class="record-actions" v-if="record.orderStatus == 0">
										<view class="action-divider"></view>
										<view class="pay-action-row">
											<text class="pay-tip">订单将为你保留6小时</text>
											<button class="pay-button" @click="payPendingOrder(record)">
												<text class="pay-button-text">立即支付</text>
											</button>
										</view>
									</view>
								</view>
							</view>
							<view class="record-empty" v-else-if="!loadingRecords">
								<u-empty
									mode="list"
									text="暂无充值记录"
									icon="http://cdn.uviewui.com/uview/empty/data.png"
									marginTop="50"
								></u-empty>
							</view>
							<view class="record-loading" v-if="loadingRecords">
								<u-loading-icon mode="flower" text="加载中..." textSize="14"></u-loading-icon>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { addRechargeOrder,payOrder,listRechargeOrder } from "@/api/buy/rechargeOrder.js"
import { getStoreInfo } from "@/api/buy/storeInfo.js"
export default {
	data() {
		return {
			userId: this.$store.state.user.userId,
			storeId: null, // 店铺ID
			currentBalance: '0.00', // 当前余额
			tabs: [
				{ name: '余额充值' },
				{ name: '充值记录' }
			],
			currentTab: 0, // 默认显示余额充值
			quickAmounts: [10, 50, 100, 200, 500, 1000], // 快捷金额
			selectedAmount: 0, // 选中的快捷金额
			customAmount: '', // 自定义金额
			rechargeRecords: [], // 充值记录
			loadingRecords: false,
			loadingBalance: false
		}
	},
	computed: {
		// 最终充值金额
		finalAmount() {
			if (this.customAmount && parseFloat(this.customAmount) > 0) {
				return parseFloat(this.customAmount).toFixed(2);
			}
			if (this.selectedAmount > 0) {
				return this.selectedAmount.toFixed(2);
			}
			return '0.00';
		},
		// 是否可以充值
		canRecharge() {
			const amount = parseFloat(this.finalAmount);
			return amount > 0 && amount <= 50000;
		},

		// 处理后的充值记录（添加状态样式类和文本）
		processedRechargeRecords() {
			return this.rechargeRecords.map(record => ({
				...record,
				statusClass: this.getStatusClass(record.orderStatus),
				statusText: this.getStatusText(record.orderStatus)
			}));
		}
	},
	onLoad(options) {
		// 从URL参数获取店铺ID，如果没有则从store获取
		this.storeId = options.storeId || this.$store.state.user.storeId;
		this.getCurrentBalance();
		this.getRechargeRecords();
	},
	methods: {
		// 切换Tab
		switchTab(index) {
			this.currentTab = index;
			if (index === 1 && this.rechargeRecords.length === 0) {
				this.getRechargeRecords();
			}
		},
		
		// 选择快捷金额
		selectAmount(amount) {
			this.selectedAmount = amount;
			this.customAmount = ''; // 清空自定义金额
		},
		
		// 自定义金额输入
		onCustomAmountInput() {
			this.selectedAmount = 0; // 清空快捷金额选择
		},
		

		
		// 获取当前余额
		async getCurrentBalance() {
			if (!this.storeId) {
				console.log('storeId不存在，无法获取余额');
				return;
			}

			this.loadingBalance = true;
			try {
				// 通过店铺ID获取店铺详细信息，其中包含余额字段
				const res = await getStoreInfo(this.storeId);

				if (res.code === 200 && res.data) {
					const storeInfo = res.data;
					this.currentBalance = storeInfo.money || '0.00';
					console.log('获取到店铺余额:', this.currentBalance);
				} else {
					console.log('获取店铺信息失败:', res.msg);
					this.currentBalance = '0.00';
				}
			} catch (error) {
				console.error('获取店铺余额失败:', error);
				this.currentBalance = '0.00';
			} finally {
				this.loadingBalance = false;
			}
		},
		
		// 获取充值记录
		async getRechargeRecords() {
			if (!this.storeId) return;

			this.loadingRecords = true;
			try {
				const params = {
					storeId: this.storeId,
					pageNum: 1,
					pageSize: 20
				};
				const res = await listRechargeOrder(params);
				if (res.code === 200) {
					// 直接使用原始数据，保留orderStatus
					this.rechargeRecords = res.rows || [];
				} else {
					this.$modal.msgError(res.msg || '获取充值记录失败');
				}
			} catch (error) {
				console.error('获取充值记录失败:', error);
				this.$modal.msgError('获取充值记录失败');
			} finally {
				this.loadingRecords = false;
			}
		},



		// 支付待支付订单
		payPendingOrder(record) {
			console.log('payPendingOrder called with record:', record);
			const self = this;
			uni.showLoading({ title: '准备支付...' });

			// 获取支付参数
			payOrder(record.orderId).then(payRes => {
				if (payRes.code !== 200) {
					uni.hideLoading();
					self.$modal.msgError(payRes.msg || '获取支付参数失败');
					return;
				}

				uni.hideLoading();

				// 调起微信支付
				self.requestWxPayment(payRes.data);

			}).catch(error => {
				uni.hideLoading();
				console.error('支付失败:', error);
				self.$modal.msgError('支付失败，请重试');
			});
		},
		
		// 处理充值
		handleRecharge() {
			if (!this.canRecharge) return;
			
			uni.showModal({
				title: '确认充值',
				content: `确认充值 ¥${this.finalAmount} 吗？`,
				success: (res) => {
					if (res.confirm) {
						this.processRecharge();
					}
				}
			});
		},
		
		// 处理充值逻辑
		async processRecharge() {
			if (!this.storeId) {
				this.$modal.msgError('店铺信息不存在');
				return;
			}

			try {
				uni.showLoading({ title: '创建订单中...' });

				// 获取微信登录code
				const loginRes = await this.getWxLoginCode();
				if (!loginRes.code) {
					uni.hideLoading();
					this.$modal.msgError('获取微信授权失败');
					return;
				}

				// 创建充值订单
				const orderData = {
					storeId: this.storeId,
					rechargeAmount: parseFloat(this.finalAmount),
					code: loginRes.code
				};

				const orderRes = await addRechargeOrder(orderData);
				if (orderRes.code !== 200) {
					uni.hideLoading();
					this.$modal.msgError(orderRes.msg || '创建订单失败');
					return;
				}

				const orderId = orderRes.data.id;
				uni.hideLoading();
				uni.showLoading({ title: '准备支付...' });

				// 获取支付参数
				const payRes = await payOrder(orderId);
				if (payRes.code !== 200) {
					uni.hideLoading();
					this.$modal.msgError(payRes.msg || '获取支付参数失败');
					return;
				}

				uni.hideLoading();

				// 调起微信支付
				await this.requestWxPayment(payRes.data);

			} catch (error) {
				uni.hideLoading();
				console.error('充值失败:', error);
				this.$modal.msgError('充值失败，请重试');
			}
		},

		// 获取微信登录code
		getWxLoginCode() {
			return new Promise((resolve, reject) => {
				uni.login({
					provider: 'weixin',
					success: (res) => {
						resolve(res);
					},
					fail: (err) => {
						reject(err);
					}
				});
			});
		},

		// 调起微信支付
		requestWxPayment(paymentData) {
			console.log('支付参数:', paymentData);
			return new Promise((resolve, reject) => {
				// 微信小程序支付参数格式
				const paymentParams = {
					timeStamp: paymentData.timeStamp,
					nonceStr: paymentData.nonceStr,
					package:`prepay_id=${paymentData.package}`,
					signType: 'RSA',
					paySign: paymentData.paySign
				};

				console.log('最终支付参数:', paymentParams);

				wx.requestPayment({
					...paymentParams,
					success: (res) => {
						console.log('支付成功:', res);
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						});

						// 重置表单
						this.selectedAmount = 0;
						this.customAmount = '';

						// 刷新余额和记录
						this.getCurrentBalance();
						this.getRechargeRecords();

						resolve(res);
					},
					fail: (err) => {
						console.error('支付失败:', err);
						if (err.errMsg && err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '支付已取消',
								icon: 'none'
							});
						} else {
							this.$modal.msgError('支付失败：' + (err.errMsg || '未知错误'));
						}
						reject(err);
					}
				});
			});
		},
		

		
		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},

		// 获取状态样式类
		getStatusClass(orderStatus) {
			const status = parseInt(orderStatus);
			switch(status) {
				case 0: return 'status-pending'; // 待支付
				case 1: return 'status-success'; // 支付成功
				case 2: return 'status-success'; // 已完成
				case 3: return 'status-failed';  // 已关闭
				case 4: return 'status-failed';  // 支付失败
				default: return 'status-pending';
			}
		},

		// 获取状态文本
		getStatusText(orderStatus) {
			const status = parseInt(orderStatus);
			switch(status) {
				case 0: return '待支付';
				case 1: return '支付成功';
				case 2: return '已完成';
				case 3: return '已关闭';
				case 4: return '支付失败';
				default: return '未知';
			}
		}
	}
}
</script>

<style lang="scss">
.page-container {
	background-color: #f7f8fa;
	min-height: 100vh;
}

.header-section {
	background: linear-gradient(135deg, #6A82FB 0%, #BFE9FF 100%);
	padding: 40rpx 40rpx 60rpx;
	color: #ffffff;

	.header-content {
		position: relative;
		z-index: 1;
		
		.page-title {
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 40rpx;
		}

		.balance-info {
			.balance-label {
				font-size: 28rpx;
				opacity: 0.9;
				margin-bottom: 8rpx;
			}
			.balance-amount-wrapper {
				display: flex;
				align-items: baseline;
				height: 70rpx; // 固定高度防止加载时跳动
			}
			.currency-symbol {
				font-size: 40rpx;
				font-weight: 500;
				margin-right: 4rpx;
			}
			.balance-amount {
				font-size: 64rpx;
				font-weight: bold;
				color: #fff;
			}
		}
	}
}

.content-wrapper {
	padding: 0 30rpx;
	position: relative;
	margin-top: -30rpx; // 卡片上移
	z-index: 2;
}

.main-card {
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.06);
	overflow: hidden;
}

.tabs-container {
	padding: 20rpx;
	background-color: #fff;
	.custom-tabs {
		background: #f0f2f5;
		border-radius: 16rpx;
		display: flex;
		padding: 6rpx;
		.tab-item {
			flex: 1;
			text-align: center;
			padding: 20rpx;
			position: relative;
			border-radius: 12rpx;
			transition: all 0.3s ease;
			.tab-text {
				font-size: 28rpx;
				color: #606266;
				font-weight: 500;
			}
			&.active {
				background: #fff;
				box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
				.tab-text {
					color: #6A82FB;
					font-weight: bold;
				}
			}
		}
	}
}

.tab-content {
	min-height: 800rpx;
}

.recharge-content {
	padding: 20rpx 40rpx 50rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #303133;
	margin-bottom: 30rpx;
	margin-top: 20rpx;
	display: block;
}

.amount-section {
	.amount-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 24rpx;
		.amount-item {
			background: #f8f9fa;
			border: 2rpx solid transparent;
			border-radius: 16rpx;
			padding: 24rpx 10rpx;
			text-align: center;
			transition: all 0.2s ease-in-out;
			&.active {
				background: #E9EDFF;
				border-color: #6A82FB;
				transform: scale(1.05);
				.amount-text {
					color: #6A82FB;
					font-weight: bold;
				}
			}
			.amount-text {
				font-size: 34rpx;
				color: #303133;
				font-weight: 500;
			}
		}
	}
}

.custom-amount-section {
	.input-wrapper {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border: 2rpx solid #f8f9fa;
		border-radius: 16rpx;
		padding: 0 30rpx;
		transition: all 0.2s ease;
		&:focus-within {
			background-color: #fff;
			border-color: #6A82FB;
		}
		.input-currency-symbol {
			font-size: 40rpx;
			font-weight: bold;
			color: #303133;
			margin-right: 16rpx;
		}
		.amount-input {
			flex: 1;
			font-size: 36rpx;
			font-weight: 500;
			color: #303133;
			height: 100rpx;
		}
	}
	.amount-tip {
		font-size: 24rpx;
		color: #909399;
		margin-top: 16rpx;
		display: block;
	}
}

.recharge-button-section {
	margin-top: 60rpx;
	.recharge-button {
		width: 100%;
		background: linear-gradient(135deg, #6A82FB 0%, #899BFF 100%);
		border: none;
		border-radius: 50rpx;
		padding: 24rpx;
		box-shadow: 0 8rpx 20rpx rgba(106, 130, 251, 0.35);
		transition: all 0.3s ease;
		&:not([disabled]):active {
			transform: scale(0.98);
			box-shadow: 0 4rpx 12rpx rgba(106, 130, 251, 0.4);
		}
		&[disabled] {
			background: #e0e0e0;
			box-shadow: none;
			color: #a0a0a0;
		}
		.button-text {
			font-size: 32rpx;
			color: #fff;
			font-weight: bold;
		}
	}
}

.record-content {
	padding: 10rpx 20rpx 20rpx;
	height: 850rpx;
	box-sizing: border-box;
}

.record-list {
	.record-item {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin: 20rpx;
		box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.07);
		.record-main-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.record-left {
			.record-type {
				font-size: 30rpx;
				font-weight: bold;
				color: #303133;
				display: block;
				margin-bottom: 8rpx;
			}
			.record-time {
				font-size: 24rpx;
				color: #909399;
			}
		}
		.record-right {
			text-align: right;
			.record-amount {
				font-size: 36rpx;
				font-weight: bold;
				color: #19be6b;
				display: block;
				margin-bottom: 8rpx;
			}
			.record-status {
				font-size: 24rpx;
				font-weight: 500;
				&.status-success { color: #19be6b; }
				&.status-pending { color: #ff9500; }
				&.status-failed { color: #fa3534; }
			}
		}
		.record-actions {
			margin-top: 20rpx;
			.action-divider {
				height: 1rpx;
				background-color: #f0f0f0;
			}
			.pay-action-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;
				.pay-tip {
					font-size: 24rpx;
					color: #909399;
				}
				.pay-button {
					background: #6A82FB;
					border: none;
					border-radius: 30rpx;
					padding: 12rpx 30rpx;
					box-shadow: 0 4rpx 12rpx rgba(106, 130, 251, 0.3);
					margin: 0;
					.pay-button-text {
						font-size: 26rpx;
						color: #fff;
						font-weight: bold;
					}
				}
			}
		}
	}
}

.record-empty, .record-loading {
	padding: 150rpx 0;
	text-align: center;
}
</style>