<template>
	<view class="page-container">
		<!-- 顶部导航 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">余额充值</text>
				<view class="balance-info">
					<text class="balance-label">当前余额</text>
					<text class="balance-amount">¥{{ currentBalance }}</text>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 自定义Tabs -->
			<view class="custom-tabs">
				<view
					class="tab-item"
					:class="{ 'active': currentTab === index }"
					v-for="(tab, index) in tabs"
					:key="index"
					@click="switchTab(index)"
				>
					<text class="tab-text">{{ tab.name }}</text>
				</view>
			</view>

			<!-- Tab内容 -->
			<view class="tab-content">
				<!-- 余额充值 -->
				<view v-if="currentTab === 0" class="recharge-content">
					<!-- 快捷金额选择 -->
					<view class="amount-section">
						<text class="section-title">选择充值金额</text>
						<view class="amount-grid">
							<view
								class="amount-item"
								:class="{ 'active': selectedAmount === amount }"
								v-for="amount in quickAmounts"
								:key="amount"
								@click="selectAmount(amount)"
							>
								<text class="amount-text">¥{{ amount }}</text>
							</view>
						</view>
					</view>

					<!-- 自定义金额输入 -->
					<view class="custom-amount-section">
						<text class="section-title">自定义金额</text>
						<view class="input-wrapper">
							<text class="currency-symbol">¥</text>
							<input
								class="amount-input"
								type="digit"
								placeholder="请输入充值金额"
								v-model="customAmount"
								@input="onCustomAmountInput"
							/>
						</view>
						<text class="amount-tip">最高充值金额：¥50000</text>
					</view>

					<!-- 充值按钮 -->
					<view class="recharge-button-section">
						<button class="recharge-button" :disabled="!canRecharge" @click="handleRecharge">
							<text class="button-text">微信支付 ¥{{ finalAmount }}</text>
						</button>
					</view>
				</view>

				<!-- 充值记录 -->
				<view v-if="currentTab === 1" class="record-content">
					<view class="record-list" v-if="rechargeRecords.length > 0">
						<view class="record-item" v-for="record in rechargeRecords" :key="record.id">
							<view class="record-info">
								<view class="record-header">
									<text class="record-type">余额充值</text>
									<text class="record-status" :class="getStatusClass(record.orderStatus)">
										{{ getStatusText(record.orderStatus) }}
									</text>
								</view>
								<view class="record-details">
									<text class="record-amount">+¥{{ record.rechargeAmount }}</text>
									<text class="record-time">{{ formatTime(record.createTime) }}</text>
								</view>
								<view class="record-method">
									<text class="method-text">微信支付</text>
								</view>
								<!-- 待支付状态显示支付按钮 -->
								<view class="record-actions" v-if="record.orderStatus === 0">
									<button class="pay-button" @click="payPendingOrder(record)">
										<text class="pay-button-text">立即支付</text>
									</button>
								</view>
							</view>
						</view>
					</view>
					<view class="record-empty" v-else-if="!loadingRecords">
						<u-empty
							mode="list"
							text="暂无充值记录"
							icon="http://cdn.uviewui.com/uview/empty/data.png"
							marginTop="50"
						></u-empty>
					</view>
					<view class="record-loading" v-if="loadingRecords">
						<u-loading-icon mode="flower" text="加载中..." textSize="14"></u-loading-icon>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { addRechargeOrder,payOrder,listRechargeOrder } from "@/api/buy/rechargeOrder.js"
export default {
	data() {
		return {
			userId: this.$store.state.user.userId,
			storeId: null, // 店铺ID
			currentBalance: '0.00', // 当前余额
			tabs: [
				{ name: '余额充值' },
				{ name: '充值记录' }
			],
			currentTab: 0, // 默认显示余额充值
			quickAmounts: [10, 50, 100, 200, 500, 1000], // 快捷金额
			selectedAmount: 0, // 选中的快捷金额
			customAmount: '', // 自定义金额
			rechargeRecords: [], // 充值记录
			loadingRecords: false,
			loadingBalance: false
		}
	},
	computed: {
		// 最终充值金额
		finalAmount() {
			if (this.customAmount && parseFloat(this.customAmount) > 0) {
				return parseFloat(this.customAmount).toFixed(2);
			}
			if (this.selectedAmount > 0) {
				return this.selectedAmount.toFixed(2);
			}
			return '0.00';
		},
		// 是否可以充值
		canRecharge() {
			const amount = parseFloat(this.finalAmount);
			return amount > 0 && amount <= 50000;
		}
	},
	onLoad(options) {
		// 从URL参数获取店铺ID，如果没有则从store获取
		this.storeId = options.storeId || this.$store.state.user.storeId;
		this.getCurrentBalance();
		this.getRechargeRecords();
	},
	methods: {
		// 切换Tab
		switchTab(index) {
			this.currentTab = index;
			if (index === 1 && this.rechargeRecords.length === 0) {
				this.getRechargeRecords();
			}
		},
		
		// 选择快捷金额
		selectAmount(amount) {
			this.selectedAmount = amount;
			this.customAmount = ''; // 清空自定义金额
		},
		
		// 自定义金额输入
		onCustomAmountInput() {
			this.selectedAmount = 0; // 清空快捷金额选择
		},
		

		
		// 获取当前余额
		getCurrentBalance() {
			// 模拟数据
			this.currentBalance = '128.50';
		},
		
		// 获取充值记录
		async getRechargeRecords() {
			if (!this.storeId) return;

			this.loadingRecords = true;
			try {
				const params = {
					storeId: this.storeId,
					pageNum: 1,
					pageSize: 20
				};
				const res = await listRechargeOrder(params);
				if (res.code === 200) {
					// 直接使用原始数据，保留orderStatus
					this.rechargeRecords = res.rows || [];
				} else {
					this.$modal.msgError(res.msg || '获取充值记录失败');
				}
			} catch (error) {
				console.error('获取充值记录失败:', error);
				this.$modal.msgError('获取充值记录失败');
			} finally {
				this.loadingRecords = false;
			}
		},

		// 获取状态样式类
		getStatusClass(orderStatus) {
			switch(orderStatus) {
				case 0: return 'status-pending'; // 待支付
				case 1: return 'status-success'; // 支付成功
				case 2: return 'status-success'; // 已完成
				case 3: return 'status-failed';  // 已关闭
				case 4: return 'status-failed';  // 支付失败
				default: return 'status-pending';
			}
		},

		// 获取状态文本
		getStatusText(orderStatus) {
			switch(orderStatus) {
				case 0: return '待支付';
				case 1: return '支付成功';
				case 2: return '已完成';
				case 3: return '已关闭';
				case 4: return '支付失败';
				default: return '未知';
			}
		},

		// 支付待支付订单
		async payPendingOrder(record) {
			try {
				uni.showLoading({ title: '准备支付...' });

				// 获取支付参数
				const payRes = await payOrder(record.orderId);
				if (payRes.code !== 200) {
					uni.hideLoading();
					this.$modal.msgError(payRes.msg || '获取支付参数失败');
					return;
				}

				uni.hideLoading();

				// 调起微信支付
				await this.requestWxPayment(payRes.data);

			} catch (error) {
				uni.hideLoading();
				console.error('支付失败:', error);
				this.$modal.msgError('支付失败，请重试');
			}
		},
		
		// 处理充值
		handleRecharge() {
			if (!this.canRecharge) return;
			
			uni.showModal({
				title: '确认充值',
				content: `确认充值 ¥${this.finalAmount} 吗？`,
				success: (res) => {
					if (res.confirm) {
						this.processRecharge();
					}
				}
			});
		},
		
		// 处理充值逻辑
		async processRecharge() {
			if (!this.storeId) {
				this.$modal.msgError('店铺信息不存在');
				return;
			}

			try {
				uni.showLoading({ title: '创建订单中...' });

				// 获取微信登录code
				const loginRes = await this.getWxLoginCode();
				if (!loginRes.code) {
					uni.hideLoading();
					this.$modal.msgError('获取微信授权失败');
					return;
				}

				// 创建充值订单
				const orderData = {
					storeId: this.storeId,
					rechargeAmount: parseFloat(this.finalAmount),
					code: loginRes.code
				};

				const orderRes = await addRechargeOrder(orderData);
				if (orderRes.code !== 200) {
					uni.hideLoading();
					this.$modal.msgError(orderRes.msg || '创建订单失败');
					return;
				}

				const orderId = orderRes.data.id;
				uni.hideLoading();
				uni.showLoading({ title: '准备支付...' });

				// 获取支付参数
				const payRes = await payOrder(orderId);
				if (payRes.code !== 200) {
					uni.hideLoading();
					this.$modal.msgError(payRes.msg || '获取支付参数失败');
					return;
				}

				uni.hideLoading();

				// 调起微信支付
				await this.requestWxPayment(payRes.data);

			} catch (error) {
				uni.hideLoading();
				console.error('充值失败:', error);
				this.$modal.msgError('充值失败，请重试');
			}
		},

		// 获取微信登录code
		getWxLoginCode() {
			return new Promise((resolve, reject) => {
				uni.login({
					provider: 'weixin',
					success: (res) => {
						resolve(res);
					},
					fail: (err) => {
						reject(err);
					}
				});
			});
		},

		// 调起微信支付
		requestWxPayment(paymentData) {
			return new Promise((resolve, reject) => {
				uni.requestPayment({
					provider: 'wxpay',
					timeStamp: paymentData.timeStamp,
					nonceStr: paymentData.nonceStr,
					package: paymentData.package,
					signType: 'MD5',
					paySign: paymentData.paySign,
					success: (res) => {
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						});

						// 重置表单
						this.selectedAmount = 0;
						this.customAmount = '';

						// 刷新余额和记录
						this.getCurrentBalance();
						this.getRechargeRecords();

						resolve(res);
					},
					fail: (err) => {
						console.error('支付失败:', err);
						if (err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '支付已取消',
								icon: 'none'
							});
						} else {
							this.$modal.msgError('支付失败，请重试');
						}
						reject(err);
					}
				});
			});
		},
		

		
		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		}
	}
}
</script>

<style lang="scss">
.page-container {
	background-color: #f7f8fa;
	min-height: 100vh;
}

.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 50rpx 40rpx 60rpx;
	color: #ffffff;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: radial-gradient(circle, rgba(255,255,255,0.1), rgba(255,255,255,0) 60%);
		animation: rotate 20s linear infinite;
	}

	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.header-content {
		position: relative;
		z-index: 1;
		text-align: center;

		.page-title {
			font-size: 44rpx;
			font-weight: bold;
			display: block;
			margin-bottom: 30rpx;
		}

		.balance-info {
			.balance-label {
				font-size: 28rpx;
				opacity: 0.9;
				display: block;
				margin-bottom: 8rpx;
			}

			.balance-amount {
				font-size: 48rpx;
				font-weight: bold;
				color: #fff;
			}
		}
	}
}

.content-wrapper {
	padding: 0 30rpx;
	position: relative;
	margin-top: -30rpx;
	z-index: 2;
}

.custom-tabs {
	background: #fff;
	border-radius: 16rpx 16rpx 0 0;
	display: flex;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 30rpx;
		position: relative;

		.tab-text {
			font-size: 32rpx;
			color: #666;
			font-weight: 500;
			transition: all 0.3s;
		}

		&.active {
			.tab-text {
				color: #667eea;
				font-weight: 600;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 6rpx;
				background-color: #667eea;
				border-radius: 3rpx;
			}
		}
	}
}

.tab-content {
	background: #fff;
	border-radius: 0 0 16rpx 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	min-height: 600rpx;
}

// 余额充值样式
.recharge-content {
	padding: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #303133;
	margin-bottom: 30rpx;
	display: block;
}

.amount-section {
	margin-bottom: 50rpx;

	.amount-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;

		.amount-item {
			background: #f8f9fa;
			border: 2rpx solid #e9ecef;
			border-radius: 12rpx;
			padding: 30rpx 20rpx;
			text-align: center;
			transition: all 0.3s ease;

			&.active {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				border-color: #667eea;
				transform: translateY(-2rpx);
				box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);

				.amount-text {
					color: #fff;
					font-weight: 600;
				}
			}

			.amount-text {
				font-size: 32rpx;
				color: #303133;
				font-weight: 500;
			}
		}
	}
}

.custom-amount-section {
	margin-bottom: 50rpx;

	.input-wrapper {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		border-radius: 12rpx;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
		min-height: 100rpx;

		.currency-symbol {
			font-size: 32rpx;
			color: #666;
			margin-right: 10rpx;
			line-height: 1;
		}

		.amount-input {
			flex: 1;
			font-size: 32rpx;
			color: #303133;
			padding: 30rpx 0;
			background: transparent;
			line-height: 1.2;
			height: 40rpx;
			display: flex;
			align-items: center;
		}
	}

	.amount-tip {
		font-size: 24rpx;
		color: #909399;
		line-height: 1.5;
	}
}



.recharge-button-section {
	.recharge-button {
		width: 100%;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border: none;
		border-radius: 50rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
		transition: all 0.3s ease;

		&:not([disabled]):active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
		}

		&[disabled] {
			background: #c8c9cc;
			box-shadow: none;
		}

		.button-text {
			font-size: 36rpx;
			color: #fff;
			font-weight: 600;
		}
	}
}

// 充值记录样式
.record-content {
	padding: 40rpx;
}

.record-list {
	.record-item {
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		border-left: 6rpx solid #667eea;

		.record-info {
			.record-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;

				.record-type {
					font-size: 32rpx;
					font-weight: 600;
					color: #303133;
				}

				.record-status {
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 24rpx;
					font-weight: 500;

					&.status-success {
						background: rgba(25, 190, 107, 0.1);
						color: #19be6b;
					}

					&.status-pending {
						background: rgba(255, 149, 0, 0.1);
						color: #ff9500;
					}

					&.status-failed {
						background: rgba(250, 53, 52, 0.1);
						color: #fa3534;
					}
				}
			}

			.record-details {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 15rpx;

				.record-amount {
					font-size: 36rpx;
					font-weight: bold;
					color: #19be6b;
				}

				.record-time {
					font-size: 26rpx;
					color: #909399;
				}
			}

			.record-method {
				.method-text {
					font-size: 26rpx;
					color: #606266;
				}
			}

			.record-actions {
				margin-top: 20rpx;
				display: flex;
				justify-content: flex-end;

				.pay-button {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					border: none;
					border-radius: 30rpx;
					padding: 12rpx 30rpx;
					box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
					transition: all 0.3s ease;

					&:active {
						transform: translateY(1rpx);
						box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
					}

					.pay-button-text {
						font-size: 26rpx;
						color: #fff;
						font-weight: 500;
					}
				}
			}
		}
	}
}

.record-empty, .record-loading {
	padding: 80rpx 0;
	text-align: center;
}
</style>
