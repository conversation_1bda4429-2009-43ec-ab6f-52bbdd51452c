<template>
	<view class="page-container">
		<!-- 顶部导航 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">余额充值</text>
				<view class="balance-info">
					<text class="balance-label">当前余额</text>
					<text class="balance-amount">¥{{ currentBalance }}</text>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 自定义Tabs -->
			<view class="custom-tabs">
				<view
					class="tab-item"
					:class="{ 'active': currentTab === index }"
					v-for="(tab, index) in tabs"
					:key="index"
					@click="switchTab(index)"
				>
					<text class="tab-text">{{ tab.name }}</text>
				</view>
			</view>

			<!-- Tab内容 -->
			<view class="tab-content">
				<!-- 余额充值 -->
				<view v-if="currentTab === 0" class="recharge-content">
					<!-- 快捷金额选择 -->
					<view class="amount-section">
						<text class="section-title">选择充值金额</text>
						<view class="amount-grid">
							<view
								class="amount-item"
								:class="{ 'active': selectedAmount === amount }"
								v-for="amount in quickAmounts"
								:key="amount"
								@click="selectAmount(amount)"
							>
								<text class="amount-text">¥{{ amount }}</text>
							</view>
						</view>
					</view>

					<!-- 自定义金额输入 -->
					<view class="custom-amount-section">
						<text class="section-title">自定义金额</text>
						<view class="input-wrapper">
							<text class="currency-symbol">¥</text>
							<input
								class="amount-input"
								type="digit"
								placeholder="请输入充值金额"
								v-model="customAmount"
								@input="onCustomAmountInput"
							/>
						</view>
						<text class="amount-tip">最低充值金额：¥10，最高充值金额：¥50000</text>
					</view>

					<!-- 支付方式 -->
					<view class="payment-section">
						<text class="section-title">支付方式</text>
						<view class="payment-methods">
							<view
								class="payment-item"
								:class="{ 'active': selectedPayment === method.value }"
								v-for="method in paymentMethods"
								:key="method.value"
								@click="selectPayment(method.value)"
							>
								<view class="payment-icon">
									<text class="icon-text">{{ method.icon }}</text>
								</view>
								<text class="payment-name">{{ method.name }}</text>
								<view class="payment-check" v-if="selectedPayment === method.value">
									<u-icon name="checkmark" size="16" color="#19be6b"></u-icon>
								</view>
							</view>
						</view>
					</view>

					<!-- 充值按钮 -->
					<view class="recharge-button-section">
						<button class="recharge-button" :disabled="!canRecharge" @click="handleRecharge">
							<text class="button-text">立即充值 ¥{{ finalAmount }}</text>
						</button>
					</view>
				</view>

				<!-- 充值记录 -->
				<view v-if="currentTab === 1" class="record-content">
					<view class="record-list" v-if="rechargeRecords.length > 0">
						<view class="record-item" v-for="record in rechargeRecords" :key="record.id">
							<view class="record-info">
								<view class="record-header">
									<text class="record-type">余额充值</text>
									<text class="record-status" :class="getStatusClass(record.status)">
										{{ getStatusText(record.status) }}
									</text>
								</view>
								<view class="record-details">
									<text class="record-amount">+¥{{ record.amount }}</text>
									<text class="record-time">{{ formatTime(record.createTime) }}</text>
								</view>
								<view class="record-method" v-if="record.paymentMethod">
									<text class="method-text">{{ getPaymentMethodName(record.paymentMethod) }}</text>
								</view>
							</view>
						</view>
					</view>
					<view class="record-empty" v-else-if="!loadingRecords">
						<u-empty
							mode="list"
							text="暂无充值记录"
							icon="http://cdn.uviewui.com/uview/empty/data.png"
							marginTop="50"
						></u-empty>
					</view>
					<view class="record-loading" v-if="loadingRecords">
						<u-loading-icon mode="flower" text="加载中..." textSize="14"></u-loading-icon>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getStoreBalance, createRechargeOrder, getRechargeRecords } from "@/api/buy/recharge.js"

export default {
	data() {
		return {
			userId: this.$store.state.user.userId,
			storeId: null, // 店铺ID
			currentBalance: '0.00', // 当前余额
			tabs: [
				{ name: '余额充值' },
				{ name: '充值记录' }
			],
			currentTab: 0, // 默认显示余额充值
			quickAmounts: [10, 50, 100, 200, 500, 1000], // 快捷金额
			selectedAmount: 0, // 选中的快捷金额
			customAmount: '', // 自定义金额
			paymentMethods: [
				{ value: 'wechat', name: '微信支付', icon: '💚' },
				{ value: 'alipay', name: '支付宝', icon: '🔵' }
			],
			selectedPayment: 'wechat', // 默认选择微信支付
			rechargeRecords: [], // 充值记录
			loadingRecords: false,
			loadingBalance: false
		}
	},
	computed: {
		// 最终充值金额
		finalAmount() {
			if (this.customAmount && parseFloat(this.customAmount) > 0) {
				return parseFloat(this.customAmount).toFixed(2);
			}
			if (this.selectedAmount > 0) {
				return this.selectedAmount.toFixed(2);
			}
			return '0.00';
		},
		// 是否可以充值
		canRecharge() {
			const amount = parseFloat(this.finalAmount);
			return amount >= 10 && amount <= 50000 && this.selectedPayment;
		}
	},
	onLoad(options) {
		// 从URL参数获取店铺ID，如果没有则从store获取
		this.storeId = options.storeId || this.$store.state.user.storeId;
		this.getCurrentBalance();
		this.getRechargeRecords();
	},
	methods: {
		// 切换Tab
		switchTab(index) {
			this.currentTab = index;
			if (index === 1 && this.rechargeRecords.length === 0) {
				this.getRechargeRecords();
			}
		},
		
		// 选择快捷金额
		selectAmount(amount) {
			this.selectedAmount = amount;
			this.customAmount = ''; // 清空自定义金额
		},
		
		// 自定义金额输入
		onCustomAmountInput() {
			this.selectedAmount = 0; // 清空快捷金额选择
		},
		
		// 选择支付方式
		selectPayment(method) {
			this.selectedPayment = method;
		},
		
		// 获取当前余额
		async getCurrentBalance() {
			if (!this.storeId) {
				this.$modal.msgError('店铺信息不存在');
				return;
			}

			this.loadingBalance = true;
			try {
				const res = await getStoreBalance(this.storeId);
				if (res.code === 200) {
					this.currentBalance = res.data.balance || '0.00';
				} else {
					this.$modal.msgError(res.msg || '获取余额失败');
				}
			} catch (error) {
				console.error('获取余额失败:', error);
				this.$modal.msgError('获取余额失败');
			} finally {
				this.loadingBalance = false;
			}
		},
		
		// 获取充值记录
		async getRechargeRecords() {
			if (!this.storeId) return;

			this.loadingRecords = true;
			try {
				const params = {
					storeId: this.storeId,
					pageNum: 1,
					pageSize: 20
				};
				const res = await getRechargeRecords(params);
				if (res.code === 200) {
					this.rechargeRecords = res.rows || [];
				} else {
					this.$modal.msgError(res.msg || '获取充值记录失败');
				}
			} catch (error) {
				console.error('获取充值记录失败:', error);
				this.$modal.msgError('获取充值记录失败');
			} finally {
				this.loadingRecords = false;
			}
		},
		
		// 处理充值
		handleRecharge() {
			if (!this.canRecharge) return;
			
			uni.showModal({
				title: '确认充值',
				content: `确认充值 ¥${this.finalAmount} 吗？`,
				success: (res) => {
					if (res.confirm) {
						this.processRecharge();
					}
				}
			});
		},
		
		// 处理充值逻辑
		async processRecharge() {
			if (!this.storeId) {
				this.$modal.msgError('店铺信息不存在');
				return;
			}

			uni.showLoading({ title: '处理中...' });

			try {
				const rechargeData = {
					storeId: this.storeId,
					amount: parseFloat(this.finalAmount),
					paymentMethod: this.selectedPayment
				};

				const res = await createRechargeOrder(rechargeData);
				if (res.code === 200) {
					uni.hideLoading();
					uni.showToast({
						title: '充值成功',
						icon: 'success'
					});

					// 重置表单
					this.selectedAmount = 0;
					this.customAmount = '';

					// 刷新余额和记录
					this.getCurrentBalance();
					this.getRechargeRecords();
				} else {
					uni.hideLoading();
					this.$modal.msgError(res.msg || '充值失败');
				}
			} catch (error) {
				uni.hideLoading();
				console.error('充值失败:', error);
				this.$modal.msgError('充值失败，请重试');
			}
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			switch(status) {
				case 'success': return 'status-success';
				case 'pending': return 'status-pending';
				case 'failed': return 'status-failed';
				default: return '';
			}
		},
		
		// 获取状态文本
		getStatusText(status) {
			switch(status) {
				case 'success': return '成功';
				case 'pending': return '处理中';
				case 'failed': return '失败';
				default: return '未知';
			}
		},
		
		// 获取支付方式名称
		getPaymentMethodName(method) {
			const paymentMethod = this.paymentMethods.find(m => m.value === method);
			return paymentMethod ? paymentMethod.name : '未知';
		},
		
		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		}
	}
}
</script>

<style lang="scss">
.page-container {
	background-color: #f7f8fa;
	min-height: 100vh;
}

.header-section {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 50rpx 40rpx 60rpx;
	color: #ffffff;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: radial-gradient(circle, rgba(255,255,255,0.1), rgba(255,255,255,0) 60%);
		animation: rotate 20s linear infinite;
	}

	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	.header-content {
		position: relative;
		z-index: 1;
		text-align: center;

		.page-title {
			font-size: 44rpx;
			font-weight: bold;
			display: block;
			margin-bottom: 30rpx;
		}

		.balance-info {
			.balance-label {
				font-size: 28rpx;
				opacity: 0.9;
				display: block;
				margin-bottom: 8rpx;
			}

			.balance-amount {
				font-size: 48rpx;
				font-weight: bold;
				color: #fff;
			}
		}
	}
}

.content-wrapper {
	padding: 0 30rpx;
	position: relative;
	margin-top: -30rpx;
	z-index: 2;
}

.custom-tabs {
	background: #fff;
	border-radius: 16rpx 16rpx 0 0;
	display: flex;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 30rpx;
		position: relative;

		.tab-text {
			font-size: 32rpx;
			color: #666;
			font-weight: 500;
			transition: all 0.3s;
		}

		&.active {
			.tab-text {
				color: #667eea;
				font-weight: 600;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 6rpx;
				background-color: #667eea;
				border-radius: 3rpx;
			}
		}
	}
}

.tab-content {
	background: #fff;
	border-radius: 0 0 16rpx 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	min-height: 600rpx;
}

// 余额充值样式
.recharge-content {
	padding: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #303133;
	margin-bottom: 30rpx;
	display: block;
}

.amount-section {
	margin-bottom: 50rpx;

	.amount-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;

		.amount-item {
			background: #f8f9fa;
			border: 2rpx solid #e9ecef;
			border-radius: 12rpx;
			padding: 30rpx 20rpx;
			text-align: center;
			transition: all 0.3s ease;

			&.active {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				border-color: #667eea;
				transform: translateY(-2rpx);
				box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);

				.amount-text {
					color: #fff;
					font-weight: 600;
				}
			}

			.amount-text {
				font-size: 32rpx;
				color: #303133;
				font-weight: 500;
			}
		}
	}
}

.custom-amount-section {
	margin-bottom: 50rpx;

	.input-wrapper {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		border-radius: 12rpx;
		padding: 0 20rpx;
		margin-bottom: 20rpx;

		.currency-symbol {
			font-size: 32rpx;
			color: #666;
			margin-right: 10rpx;
		}

		.amount-input {
			flex: 1;
			font-size: 32rpx;
			color: #303133;
			padding: 30rpx 0;
			background: transparent;
		}
	}

	.amount-tip {
		font-size: 24rpx;
		color: #909399;
		line-height: 1.5;
	}
}

.payment-section {
	margin-bottom: 60rpx;

	.payment-methods {
		.payment-item {
			display: flex;
			align-items: center;
			background: #f8f9fa;
			border: 2rpx solid #e9ecef;
			border-radius: 12rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			transition: all 0.3s ease;

			&.active {
				border-color: #667eea;
				background: rgba(102, 126, 234, 0.05);
			}

			.payment-icon {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				background: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 20rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

				.icon-text {
					font-size: 32rpx;
				}
			}

			.payment-name {
				flex: 1;
				font-size: 32rpx;
				color: #303133;
				font-weight: 500;
			}

			.payment-check {
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
				background: #19be6b;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}

.recharge-button-section {
	.recharge-button {
		width: 100%;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border: none;
		border-radius: 50rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
		transition: all 0.3s ease;

		&:not([disabled]):active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
		}

		&[disabled] {
			background: #c8c9cc;
			box-shadow: none;
		}

		.button-text {
			font-size: 36rpx;
			color: #fff;
			font-weight: 600;
		}
	}
}

// 充值记录样式
.record-content {
	padding: 40rpx;
}

.record-list {
	.record-item {
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		border-left: 6rpx solid #667eea;

		.record-info {
			.record-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;

				.record-type {
					font-size: 32rpx;
					font-weight: 600;
					color: #303133;
				}

				.record-status {
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 24rpx;
					font-weight: 500;

					&.status-success {
						background: rgba(25, 190, 107, 0.1);
						color: #19be6b;
					}

					&.status-pending {
						background: rgba(255, 149, 0, 0.1);
						color: #ff9500;
					}

					&.status-failed {
						background: rgba(250, 53, 52, 0.1);
						color: #fa3534;
					}
				}
			}

			.record-details {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 15rpx;

				.record-amount {
					font-size: 36rpx;
					font-weight: bold;
					color: #19be6b;
				}

				.record-time {
					font-size: 26rpx;
					color: #909399;
				}
			}

			.record-method {
				.method-text {
					font-size: 26rpx;
					color: #606266;
				}
			}
		}
	}
}

.record-empty, .record-loading {
	padding: 80rpx 0;
	text-align: center;
}
</style>
