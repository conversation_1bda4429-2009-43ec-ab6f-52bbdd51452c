<template>
	<view class="page-container">
		<!-- 城市选择弹窗 -->
		<uni-popup ref="cityPopup" type="left">
			<view class="city-popup-content">
				<view class="popup-title">选择城市</view>
				<scroll-view scroll-y class="city-scroll-view">
					<view 
						v-for="city in cities" 
						:key="city.id"
						class="city-item"
						:class="{ 'active': city.id === currentCityId }"
						@click="selectCity(city.id)">
						{{ city.name }}
					</view>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 顶部区域 -->
		<view class="header-section">
			<view class="header-content">
				<view class="location-selector" @click="openCityPopup">
					<uni-icons type="location-filled" size="18" color="#333"></uni-icons>
					<text class="city-name">{{ currentCity.name || '选择城市' }}</text>
				</view>
				<view class="search-bar">
					<view class="search-input-wrapper" @click="goToSearch">
						<uni-icons type="search" size="18" color="#999"></uni-icons>
						<input class="search-input" type="text" v-model="keyword" placeholder="请输入关键词" />
					</view>
					<button class="search-btn" @click="goToSearch">搜索</button>
				</view>
			</view>
		</view>

		<!-- 主内容区域 -->
		<scroll-view scroll-y class="content-scroll-view" :scroll-top="scrollTop">
			<view v-if="loading" class="loading-state">正在加载...</view>
			<view v-else-if="marketsToShow.length === 0" class="empty-state">
				<text>当前城市下暂无市场信息</text>
			</view>
			<template v-else>
				<view class="market-card" v-for="market in marketsToShow" :key="market.id">
					<view class="market-header">
						<text class="market-title">{{ market.name }}</text>
						<view class="view-all" @click="toggleMarketExpansion(market)" v-if="market.children.length > 10">
							<text>{{ market.isExpanded ? '收起' : '查看全部' }}</text>
							<uni-icons :type="market.isExpanded ? 'up' : 'right'" size="14" color="#999"></uni-icons>
						</view>
					</view>
					<view class="category-grid">
						<!-- **关键修复**: 将复杂的 v-for 源改为调用一个方法 -->
						<view class="category-item" v-for="industry in getDisplayIndustries(market)" :key="industry.id" @click="goToIndustry(industry.id)">
							<image class="category-icon" :src="baseUrl + industry.iconUrl" mode="aspectFill" />
							<text class="category-name">{{ industry.name }}</text>
						</view>
					</view>
				</view>
			</template>
		</scroll-view>

		<!-- 悬浮按钮组 -->
		<view class="floating-buttons">
			<!-- 买货群按钮 -->
			<view class="floating-btn buy-group-btn" @click="goToBuyGroup">
				<text class="btn-icon">👥</text>
				<text class="btn-text">买货群</text>
			</view>

			<!-- 申请入驻按钮 -->
			<view class="floating-btn apply-btn" @click="goToApply">
				<text class="btn-icon">🏪</text>
				<text class="btn-text">申请入驻</text>
			</view>

			<!-- 返回顶部按钮 -->
			<view class="floating-btn back-top-btn" @click="backToTop">
				<text class="btn-icon">⬆️</text>
				<text class="btn-text">返回顶部</text>
			</view>
		</view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar ref="customTabbar" :current="2" @change="onTabChange"></custom-tabbar>
	</view>
</template>

<script>
	import { listNodes } from "@/api/buy/nodes.js"
	import { baseUrl } from "@/config";
	import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				loading: true,
				allNodes: [],
				cities: [],
				currentCityId: null,
				keyword: '',
				baseUrl,
				locationProcessed: false, // 标记是否已经处理过定位
				scrollTop: 0 // 控制scroll-view的滚动位置
			}
		},
		computed: {
			currentCity() {
				return this.cities.find(c => String(c.id) === String(this.currentCityId)) || {};
			},
			marketsToShow() {
				if (!this.currentCityId || this.allNodes.length === 0) {
					return [];
				}
				// 确保id比较时类型一致
				const city = this.allNodes.find(node => String(node.id) === String(this.currentCityId));
				return city ? city.children : [];
			}
		},
		onLoad: function() {
			this.initPage();
		},

		onShow: function() {
			// 同步tabBar状态
			this.$nextTick(() => {
				if (this.$refs.customTabbar) {
					this.$refs.customTabbar.syncCurrentPageState();
				}
			});
		},
		methods: {
			// 初始化页面
			async initPage() {
				// 先加载数据
				await this.fetchData();
				// 数据加载完成后获取用户定位
				this.getUserLocation();

				// 设置超时，如果10秒内没有设置城市，则强制设置默认城市
				setTimeout(() => {
					if (!this.currentCityId && !this.locationProcessed) {
						this.forceSetDefaultCity();
					}
				}, 10000);
			},

			async fetchData() {
				this.loading = true;
				try {
					const res = await listNodes({pageSize:10000000});
					const flatList = res.rows || [];

					const nodeMap = new Map();
					flatList.forEach(node => {
						node.children = [];
						if (node.nodeType === 'market') {
							node.isExpanded = false;
						}
						// 确保id和parentId都转换为字符串进行统一处理
						nodeMap.set(String(node.id), node);
					});

					const tree = [];
					flatList.forEach(node => {
						// 统一使用字符串类型进行比较
						const parentId = String(node.parentId);
						const parent = nodeMap.get(parentId);
						if (parent && parentId !== '0') {
							parent.children.push(node);
						} else {
							tree.push(node);
						}
					});
					
					this.allNodes = tree;

					// 先尝试原来的过滤方式
					let cities = tree.filter(node => node.nodeType === 'city');

					// 如果没有找到，尝试其他可能的字段名
					if (cities.length === 0) {
						cities = tree.filter(node =>
							node.type === 'city' ||
							node.level === 1 ||
							node.parentId === 0
						);
					}

					// 进一步过滤确保有名称
					this.cities = cities.filter(node =>
						node.nodeName || node.name || node.cityName
					);

					// 数据加载完成，不在这里设置默认城市
					// 让getUserLocation()来处理城市设置

				} catch (error) {
					console.error("Failed to fetch data:", error);
					uni.showToast({ title: '数据加载失败', icon: 'none' });
				} finally {
					this.loading = false;
				}
			},
			// **新增方法**：用于从模板中安全地获取要显示的行业列表
			getDisplayIndustries(market) {
				if (!market || !Array.isArray(market.children)) {
					return []; // 防御式编程，防止market或其children不存在
				}
				return market.isExpanded ? market.children : market.children.slice(0, 10);
			},
			toggleMarketExpansion(market) {
				market.isExpanded = !market.isExpanded;
			},
			openCityPopup() {
				this.$refs.cityPopup.open();
			},
			selectCity(cityId) {
				this.currentCityId = cityId;
				this.$refs.cityPopup.close();
			},
			// 跳转到搜索页面
			goToSearch() {
				// 携带当前城市信息
				const cityInfo = {
					id: this.currentCityId,
					name: this.currentCity.name
				};

				const params = {
					cityInfo: encodeURIComponent(JSON.stringify(cityInfo))
				};

				const query = Object.keys(params).map(key => `${key}=${params[key]}`).join('&');
				uni.navigateTo({
					url: `/pages/search/index?${query}`
				});
			},
			goToIndustry(industryId) {
				this.$tab.navigateTo(`/pages/storeList/storeList?id=${industryId}`);
			},

			// tabBar切换事件
			onTabChange() {
				// 自定义tabBar组件内部已经处理了页面跳转
			},

			// 跳转到买货群
			goToBuyGroup() {
				uni.navigateTo({
					url: '/pages/buyGroup/index'
				});
			},

			// 申请入驻
			goToApply() {
				uni.navigateTo({
					url: '/pages/mine/storeInfo/storeInfo'
				});
			},

			// 返回顶部
			backToTop() {
				// 对于scroll-view组件，需要通过修改scrollTop来实现滚动
				this.scrollTop = this.scrollTop === 0 ? 1 : 0;
				// 使用nextTick确保DOM更新后再设置为0，实现滚动动画效果
				this.$nextTick(() => {
					this.scrollTop = 0;
				});
			},

			// 获取用户定位
			getUserLocation(retryCount = 0) {
				// 如果已经处理过定位，则不再处理
				if (this.locationProcessed) {
					return;
				}

				// 如果城市数据还没加载完成，等待一下再试（最多重试10次）
				if (this.cities.length === 0) {
					if (retryCount < 10) {
						setTimeout(() => {
							this.getUserLocation(retryCount + 1);
						}, 300);
						return;
					} else {
						this.locationProcessed = true;
						return;
					}
				}

				// 先检查定位权限
				uni.getSetting({
					success: (res) => {
						if (res.authSetting['scope.userLocation'] === false) {
							// 用户之前拒绝了定位权限，询问是否重新授权
							this.askForLocationPermission();
						} else if (res.authSetting['scope.userLocation'] === true) {
							// 已有权限，直接获取定位
							this.getLocation();
						} else {
							// 首次请求，直接调用获取定位（会自动弹出授权）
							this.getLocation();
						}
					},
					fail: () => {
						this.setDefaultCity();
					}
				});
			},

			// 询问用户是否授权定位
			askForLocationPermission() {
				uni.showModal({
					title: '定位权限',
					content: '为了为您推荐附近的商家，需要获取您的位置信息，是否授权？',
					confirmText: '授权',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 用户同意，打开设置页面
							uni.openSetting({
								success: (settingRes) => {
									if (settingRes.authSetting['scope.userLocation']) {
										// 用户在设置页面开启了定位权限
										this.getLocation();
									} else {
										// 用户仍然拒绝，使用默认城市
										this.setDefaultCity();
									}
								},
								fail: () => {
									this.setDefaultCity();
								}
							});
						} else {
							// 用户拒绝，使用默认城市
							this.setDefaultCity();
						}
					}
				});
			},

			// 获取定位
			getLocation() {
				uni.getLocation({
					type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
					success: (res) => {
						this.parseLocationToCity(res.latitude, res.longitude);
					},
					fail: (err) => {
						if (err.errMsg.includes('auth deny')) {
							// 用户拒绝了定位权限
							this.askForLocationPermission();
						} else {
							// 其他错误，使用默认城市
							this.setDefaultCity();
						}
					}
				});
			},

			// 解析经纬度为城市信息
			parseLocationToCity(latitude, longitude) {
				const key = 'OYKBZ-DAB65-HAOIY-IP4UA-2EG2O-DUBHC'; // 腾讯地图API密钥
				const url = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${key}`;

				uni.request({
					url: url,
					success: (mapRes) => {
						if (mapRes.data && mapRes.data.status === 0 && mapRes.data.result) {
							const city = mapRes.data.result.address_component.city;
							this.matchCityWithNodes(city);
						} else {
							this.setDefaultCity();
						}
					},
					fail: () => {
						this.setDefaultCity();
					}
				});
			},

			// 匹配城市与nodes数据
			matchCityWithNodes(userCity) {
				if (!userCity || this.cities.length === 0) {
					this.setDefaultCity();
					return;
				}

				// 清理用户城市名称（去掉"市"字和其他后缀）
				const cleanUserCity = this.cleanCityName(userCity);

				// 在cities中查找匹配的城市
				const matchedCity = this.cities.find(city => {
					// 确保city存在
					if (!city) {
						return false;
					}

					// 获取城市名称，尝试多个可能的字段
					const cityName = city.nodeName || city.name || city.cityName;
					if (!cityName) {
						return false;
					}

					// 清理后台城市名称
					const cleanCityName = this.cleanCityName(cityName);

					// 多种匹配方式
					return this.isCityMatch(cleanUserCity, cleanCityName, userCity, cityName);
				});

				if (matchedCity) {
					this.currentCityId = matchedCity.id;
				} else {
					this.setDefaultCity();
				}

				// 标记定位处理完成
				this.locationProcessed = true;
			},

			// 清理城市名称，去掉各种后缀
			cleanCityName(cityName) {
				if (!cityName) return '';

				return cityName
					.replace(/市$/, '')        // 去掉"市"
					.replace(/自治区$/, '')     // 去掉"自治区"
					.replace(/特别行政区$/, '') // 去掉"特别行政区"
					.replace(/地区$/, '')      // 去掉"地区"
					.replace(/盟$/, '')        // 去掉"盟"
					.replace(/州$/, '')        // 去掉"州"
					.trim();
			},

			// 判断两个城市名是否匹配
			isCityMatch(cleanUserCity, cleanCityName, originalUserCity, originalCityName) {
				// 1. 清理后的名称完全匹配
				if (cleanUserCity === cleanCityName) {
					return true;
				}

				// 2. 原始名称完全匹配
				if (originalUserCity === originalCityName) {
					return true;
				}

				// 3. 清理后的名称包含关系（双向）
				if (cleanUserCity.includes(cleanCityName) || cleanCityName.includes(cleanUserCity)) {
					return true;
				}

				// 4. 原始名称包含关系（双向）
				if (originalUserCity.includes(originalCityName) || originalCityName.includes(originalUserCity)) {
					return true;
				}

				// 5. 特殊情况处理：北京、上海、天津、重庆等直辖市
				const municipalities = ['北京', '上海', '天津', '重庆'];
				if (municipalities.includes(cleanUserCity) || municipalities.includes(cleanCityName)) {
					return cleanUserCity === cleanCityName;
				}

				// 6. 处理简称匹配（如：内蒙古 <-> 内蒙）
				const shortNames = {
					'内蒙古': '内蒙',
					'黑龙江': '黑龙江',
					'新疆维吾尔': '新疆',
					'西藏自治区': '西藏',
					'宁夏回族': '宁夏',
					'广西壮族': '广西'
				};

				for (const [full, short] of Object.entries(shortNames)) {
					if ((cleanUserCity.includes(full) && cleanCityName.includes(short)) ||
						(cleanUserCity.includes(short) && cleanCityName.includes(full))) {
						return true;
					}
				}

				return false;
			},

			// 设置默认城市（第一个城市）
			setDefaultCity() {
				if (this.cities.length > 0) {
					this.currentCityId = this.cities[0].id;
				}
				// 标记定位处理完成
				this.locationProcessed = true;
			},

			// 强制设置默认城市（在数据加载完成但没有定位时使用）
			forceSetDefaultCity() {
				if (this.cities.length > 0 && !this.currentCityId) {
					this.currentCityId = this.cities[0].id;
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
	}

	.page-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		padding-bottom: 120rpx; /* 为自定义tabBar预留空间 */
	}

	/* 悬浮按钮组样式 */
	.floating-buttons {
		position: fixed;
		right: 30rpx;
		bottom: 200rpx; /* 在tabBar上方 */
		z-index: 999;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.floating-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;

		&:active {
			transform: scale(0.95);
		}

		.btn-icon {
			font-size: 36rpx;
			margin-bottom: 6rpx;
			filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
		}

		.btn-text {
			font-size: 20rpx;
			font-weight: 600;
			text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
		}

		// 添加光泽效果
		&::before {
			content: '';
			position: absolute;
			top: -50%;
			left: -50%;
			width: 200%;
			height: 200%;
			background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
			transform: rotate(45deg);
			transition: all 0.6s ease;
			opacity: 0;
		}

		&:active::before {
			opacity: 1;
			animation: shine 0.6s ease;
		}
	}

	/* 买货群按钮样式 */
	.buy-group-btn {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
		color: #fff;

		&:hover {
			box-shadow: 0 12rpx 28rpx rgba(79, 172, 254, 0.4);
		}
	}

	/* 申请入驻按钮样式 */
	.apply-btn {
		background: linear-gradient(135deg, #ff6b6b 0%, #fa3534 50%, #e91e63 100%);
		color: #fff;

		&:hover {
			box-shadow: 0 12rpx 28rpx rgba(250, 53, 52, 0.4);
		}
	}

	/* 返回顶部按钮样式 */
	.back-top-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;

		&:hover {
			box-shadow: 0 12rpx 28rpx rgba(102, 126, 234, 0.4);
		}
	}

	@keyframes shine {
		0% {
			transform: translateX(-100%) translateY(-100%) rotate(45deg);
		}
		100% {
			transform: translateX(100%) translateY(100%) rotate(45deg);
		}
	}
	
	.header-section {
		padding: 20rpx 30rpx;
		background-color: transparent;
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}
	
	.location-selector {
		display: flex;
		align-items: center;
		white-space: nowrap;
		.city-name {
			margin-left: 10rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
		}
	}

	.search-bar {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #fff;
		border: 1px solid #DD1A21;
		border-radius: 40rpx;
		padding: 0 10rpx;
		height: 80rpx;

		.search-input-wrapper {
			flex: 1;
			display: flex;
			align-items: center;
			padding: 0 20rpx;
			.search-input {
				width: 100%;
				font-size: 28rpx;
				margin-left: 10rpx;
			}
		}

		.search-btn {
			background-color: #DD1A21;
			color: #fff;
			height: 64rpx;
			line-height: 64rpx;
			font-size: 28rpx;
			border-radius: 32rpx;
			padding: 0 40rpx;
			margin: 0;
			
			&::after {
				border: none;
			}
		}
	}

	.content-scroll-view {
		flex: 1;
		height: 0;
		padding: 0 30rpx;
	}

	.market-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

		.market-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;

			.market-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}
			
			.view-all {
				display: flex;
				align-items: center;
				font-size: 26rpx;
				color: #999;
			}
		}

		.category-grid {
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			gap: 30rpx;

			.category-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.category-icon {
					width: 90rpx;
					height: 90rpx;
					border-radius: 50%;
					background-color: #f0f0f0;
					margin-bottom: 15rpx;
				}

				.category-name {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
	
	.city-popup-content {
		width: 400rpx;
		height: 100vh;
		background-color: #fff;
		display: flex;
		flex-direction: column;

		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			padding: 40rpx 30rpx;
			border-bottom: 1px solid #f5f5f5;
		}
		
		.city-scroll-view {
			flex: 1;
			height: 0;
		}

		.city-item {
			padding: 25rpx 30rpx;
			font-size: 28rpx;
			border-bottom: 1px solid #f5f5f5;

			&.active {
				background-color: #fef0f0;
				color: #DD1A21;
				font-weight: 500;
			}
		}
	}
	
	.loading-state, .empty-state {
		text-align: center;
		color: #999;
		padding-top: 200rpx;
	}
</style>