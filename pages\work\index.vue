<template>
	<view class="work-container">
		<!-- 顶部tabs -->
		<view class="tabs-container">
			<view class="tabs-wrapper">
				<view
					class="tab-item"
					:class="{ active: activeTab === 'visitor' }"
					@click="switchTab('visitor')"
				>
					<text class="tab-text">实时访客</text>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'circle' }"
					@click="switchTab('circle')"
				>
					<text class="tab-text">易购圈</text>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'search' }"
					@click="switchTab('search')"
				>
					<text class="tab-text">搜索访客</text>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content-container">
			<!-- 实时访客 -->
			<view v-if="activeTab === 'visitor'" class="tab-content visitor-tab">
				<!-- 二级筛选框 -->
				<view class="filter-container">
					<!-- 第一级筛选（市场） -->
					<view class="filter-section">
						<view class="filter-header">
							<text class="filter-icon">🏪</text>
							<text class="filter-title">市场</text>
						</view>
						<scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
							<view class="filter-tags">
								<view
									v-for="market in marketList"
									:key="market.id"
									class="filter-tag"
									:class="{ active: selectedMarket.id === market.id }"
									@click="selectMarket(market)"
								>
									<text class="tag-text">{{ market.name }}</text>
								</view>
							</view>
						</scroll-view>
					</view>

					<!-- 第二级筛选（分类） -->
					<view v-if="categoryList.length > 0" class="filter-section">
						<view class="filter-header">
							<text class="filter-icon">🏷️</text>
							<text class="filter-title">分类</text>
						</view>
						<scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
							<view class="filter-tags">
								<view
									v-for="category in categoryList"
									:key="category.id"
									class="filter-tag"
									:class="{ active: selectedCategory.id === category.id }"
									@click="selectCategory(category)"
								>
									<text class="tag-text">{{ category.name }}</text>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>

				<!-- 访问记录列表 -->
				<view class="visitor-list-container">
					<scroll-view
						class="visitor-scroll"
						scroll-y="true"
						@scrolltolower="loadMoreVisitors"
						:refresher-enabled="true"
						:refresher-triggered="visitorRefreshing"
						@refresherrefresh="onVisitorRefresh"
					>
						<view class="visitor-list">
							<view
								v-for="visitor in visitorList"
								:key="visitor.id"
								class="visitor-item"
							>
								<!-- 时间轴竖线 -->
								<view class="timeline-line"></view>
								<view class="timeline-dot"></view>

								<image
									:src="getUserAvatar(visitor.avatar)"
									class="visitor-avatar"
									mode="aspectFill"
								></image>
								<view class="visitor-info">
									<text class="visitor-name">{{ visitor.nickName || '匿名用户' }}</text>
									<view class="visitor-path">
										<rich-text :nodes="getVisitorPathRich(visitor.targetId)"></rich-text>
									</view>
									<text class="visitor-time">{{ formatTime(visitor.createTime) }}</text>
								</view>
							</view>

							<!-- 加载更多 -->
							<view v-if="hasMoreVisitors" class="load-more">
								<text>加载更多...</text>
							</view>
							<view v-else-if="visitorList.length > 0" class="no-more">
								<text>没有更多了</text>
							</view>
							<view v-else class="empty-visitors">
								<text>暂无访问记录</text>
							</view>
						</view>
					</scroll-view>
				</view>


			</view>

			<!-- 易购圈 -->
			<view v-if="activeTab === 'circle'" class="tab-content circle-tab">
				<!-- 圈子列表 -->
				<view class="circle-list-container">
					<scroll-view class="circle-scroll" scroll-x="true" show-scrollbar="false">
						<view class="circle-list">
							<view
								v-for="(circle, index) in circleList"
								:key="circle.circleId"
								class="circle-item"
								:class="{ active: selectedCircleId === circle.circleId }"
								@click="selectCircle(circle)"
							>
								<image
									v-if="circle.avatar"
									:src="getImageUrl(circle.avatar)"
									class="circle-avatar"
									mode="aspectFill"
								></image>
								<view v-else class="circle-avatar default-avatar">
									<text class="avatar-text">{{ circle.name.charAt(0) }}</text>
								</view>
								<text class="circle-name">{{ circle.name }}</text>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 帖子列表区域 -->
				<view class="posts-container">
					<text class="posts-title">{{ selectedCircleName }}的帖子</text>
					<scroll-view
						class="posts-scroll"
						scroll-y="true"
						@scrolltolower="loadMorePosts"
						:refresher-enabled="true"
						:refresher-triggered="circleRefreshing"
						@refresherrefresh="onCircleRefresh"
					>
						<view class="posts-list">
							<view
								v-for="post in postList"
								:key="post.postId"
								class="post-item"
								@click="goToPostDetail(post.postId)"
							>
								<!-- 用户信息 -->
								<view class="post-header">
									<image
										:src="getUserAvatar(post.userAvatar)"
										class="user-avatar"
										mode="aspectFill"
									></image>
									<view class="user-info">
										<text class="user-name">{{ post.nickName || '匿名用户' }}</text>
										<text class="post-time">{{ formatTime(post.createTime) }}</text>
									</view>
								</view>

								<!-- 帖子内容 -->
								<view class="post-content">
									<text class="content-text">{{ getContentPreview(post.content) }}</text>
								</view>

								<!-- 图片九宫格 -->
								<view v-if="post.images" class="post-images">
									<view class="image-grid">
										<image
											v-for="(image, index) in post.imageList"
											:key="index"
											:src="getImageUrl(image)"
											class="grid-image"
											:class="post.imageClass"
											mode="aspectFill"
											@click.stop="previewImage(post.imageList, index)"
										></image>
									</view>
								</view>


							</view>

							<!-- 加载更多 -->
							<view v-if="hasMore" class="load-more">
								<text>加载更多...</text>
							</view>
							<view v-else-if="postList.length > 0" class="no-more">
								<text>没有更多了</text>
							</view>
							<view v-else class="empty-posts">
								<text>暂无帖子</text>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 发布按钮 -->
				<view class="publish-btn" @click="goToPublish">
					<text class="publish-icon">+</text>
				</view>
			</view>

			<!-- 搜索访客 -->
			<view v-if="activeTab === 'search'" class="tab-content search-tab">
				<scroll-view
					class="search-log-list"
					scroll-y="true"
					@scrolltolower="loadMoreSearchLogs"
					:refresher-enabled="true"
					:refresher-triggered="searchRefreshing"
					@refresherrefresh="onSearchRefresh"
				>
					<view v-if="safeSearchLogList.length > 0">
						<view
							v-for="log in safeSearchLogList"
							:key="log.id"
							class="search-log-item"
						>
							<!-- 时间轴竖线 -->
							<view class="timeline-line"></view>
							<!-- 时间轴圆点 -->
							<view class="timeline-dot"></view>

							<view class="log-header">
								<image
									:src="getUserAvatar(log.avatar)"
									class="user-avatar"
									mode="aspectFill"
								></image>
								<view class="log-content">
									<view class="search-text">
										<text class="user-name">{{ getUserName(log.nickName, log.userId) }}</text>
										<text class="normal-text">通过</text>
										<text class="search-label">搜索功能</text>
										<text class="search-separator"> | </text>
										<text class="search-action">搜索</text>
										<text class="search-keyword">{{ log.keyword }}</text>
									</view>
									<view class="log-meta">
										<text class="log-time">{{ formatTime(log.createTime) }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 空状态 -->
					<view v-else-if="!searchLoading && searchLogExecuted" class="empty-state">
						<text class="empty-emoji">🔍</text>
						<text class="empty-text">暂无搜索记录</text>
					</view>

					<!-- 加载更多 -->
					<view v-if="searchLoading" class="loading-more">
						<uni-icons type="spinner-cycle" size="30" color="#999"></uni-icons>
						<text class="loading-text">加载中...</text>
					</view>

					<!-- 没有更多数据 -->
					<view v-if="!searchHasMore && searchLogExecuted && safeSearchLogList.length > 0" class="no-more">
						<text class="no-more-text">没有更多数据了</text>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar ref="customTabbar" :current="3" @change="onTabChange"></custom-tabbar>
	</view>
</template>

<script>
import { listCircle } from '@/api/buy/circle'
import { listCirclePost } from '@/api/buy/circlePost'
import { listNodes } from '@/api/buy/nodes'
import { listSeeLog } from '@/api/buy/seeLog'
import { listSearchLog } from '@/api/buy/searchLog'
import config from '@/config'
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

export default {
	components: {
		CustomTabbar
	},
	data() {
		return {
			activeTab: 'visitor', // 默认选中实时访客
			// 易购圈相关数据
			circleList: [], // 圈子列表
			selectedCircleId: null, // 选中的圈子ID
			selectedCircleName: '', // 选中的圈子名称
			postList: [], // 帖子列表
			pageNum: 1, // 当前页码
			pageSize: 10, // 每页数量
			hasMore: true, // 是否还有更多数据
			loading: false, // 是否正在加载
			circleRefreshing: false, // 易购圈刷新状态
			// 实时访客相关数据
			marketList: [], // 市场列表（二级节点）
			categoryList: [], // 分类列表（三级节点）
			selectedMarket: {}, // 选中的市场
			selectedCategory: {}, // 选中的分类
			visitorList: [], // 访客列表
			visitorPageNum: 1, // 访客页码
			visitorPageSize: 20, // 访客每页数量
			hasMoreVisitors: true, // 是否还有更多访客
			loadingVisitors: false, // 是否正在加载访客
			visitorRefreshing: false, // 访客刷新状态
			allNodesMap: {}, // 所有节点的映射表，用于快速查找
			// 搜索访客相关数据
			searchLogList: [], // 搜索记录列表
			searchPageNum: 1, // 搜索记录页码
			searchPageSize: 20, // 搜索记录每页数量
			searchHasMore: true, // 是否还有更多搜索记录
			searchLoading: false, // 是否正在加载搜索记录
			searchRefreshing: false, // 是否正在刷新搜索记录
			searchLogExecuted: false, // 是否已执行过搜索记录查询
			baseUrl: config.baseUrl
		}
	},
	computed: {
		// 安全的搜索记录列表
		safeSearchLogList() {
			return this.searchLogList || [];
		}
	},
	onLoad() {
		// 页面加载时初始化数据
		if (this.activeTab === 'visitor') {
			this.getNodesData();
		} else if (this.activeTab === 'circle') {
			this.getCircleList();
		} else if (this.activeTab === 'search') {
			this.getSearchLogList();
		}
	},
	onShow() {
		// 页面显示时刷新数据
		if (this.activeTab === 'circle' && this.selectedCircleId) {
			this.getPostList();
		}

		// 同步tabBar状态
		this.$nextTick(() => {
			if (this.$refs.customTabbar) {
				this.$refs.customTabbar.syncCurrentPageState();
			}
		});
	},
	methods: {
		// 切换tab
		switchTab(tab) {
			this.activeTab = tab;
			if (tab === 'circle' && this.circleList.length === 0) {
				this.getCircleList();
			} else if (tab === 'visitor' && this.marketList.length === 0) {
				this.getNodesData();
			} else if (tab === 'search' && this.safeSearchLogList.length === 0) {
				this.getSearchLogList();
			}
		},
		// 获取圈子列表
		async getCircleList() {
			try {
				const response = await listCircle({});
				if (response.code === 200) {
					this.circleList = response.rows || [];
					// 默认选中第一个圈子
					if (this.circleList.length > 0) {
						this.selectCircle(this.circleList[0]);
					}
				}
			} catch (error) {
				console.error('获取圈子列表失败:', error);
				uni.showToast({
					title: '获取圈子列表失败',
					icon: 'none'
				});
			}
		},

		// 选择圈子
		selectCircle(circle) {
			this.selectedCircleId = circle.circleId;
			this.selectedCircleName = circle.name;
			// 获取该圈子的帖子列表
			this.getPostList();
		},

		// 获取帖子列表
		async getPostList(loadMore = false) {
			if (this.loading) return;
			if (!this.selectedCircleId) return;

			this.loading = true;

			try {
				const params = {
					circleId: this.selectedCircleId,
					pageNum: loadMore ? this.pageNum : 1,
					pageSize: this.pageSize
				};

				const response = await listCirclePost(params);
				if (response.code === 200) {
					const newPosts = response.rows || [];

					// 预处理帖子数据，添加图片列表和样式类
					const processedPosts = newPosts.map(post => {
						const imageList = this.getImageList(post.images).slice(0, 9);
						return {
							...post,
							imageList: imageList,
							imageClass: this.getImageClass(imageList.length)
						};
					});

					if (loadMore) {
						this.postList = [...this.postList, ...processedPosts];
					} else {
						this.postList = processedPosts;
						this.pageNum = 1;
					}

					// 判断是否还有更多数据
					this.hasMore = newPosts.length === this.pageSize;
					if (loadMore && newPosts.length > 0) {
						this.pageNum++;
					}
				}
			} catch (error) {
				console.error('获取帖子列表失败:', error);
				uni.showToast({
					title: '获取帖子失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 加载更多帖子
		loadMorePosts() {
			if (this.hasMore && !this.loading) {
				this.getPostList(true);
			}
		},

		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '';
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			return this.baseUrl + imagePath;
		},

		// 跳转到发布页面
		goToPublish() {
			uni.navigateTo({
				url: '/pages/circle/publish/publish?circleId=' + this.selectedCircleId
			});
		},

		// 跳转到帖子详情
		goToPostDetail(postId) {
			uni.navigateTo({
				url: '/pages/circle/detail/detail?postId=' + postId
			});
		},

		// 获取用户头像
		getUserAvatar(avatar) {
			if (!avatar) {
				return '/static/images/profile.jpg';
			}
			return this.getImageUrl(avatar);
		},

		// 获取内容预览
		getContentPreview(content) {
			if (!content) return '';
			return content.length > 100 ? content.substring(0, 100) + '...' : content;
		},

		// 获取图片列表
		getImageList(images) {
			if (!images) return [];
			return images.split(',').filter(img => img.trim());
		},

		// 获取图片样式类
		getImageClass(count) {
			if (count === 1) return 'single';
			if (count === 2) return 'double';
			if (count === 4) return 'four';
			return 'multiple';
		},

		// 预览图片
		previewImage(images, current) {
			const urls = images.map(img => this.getImageUrl(img));
			uni.previewImage({
				urls: urls,
				current: current
			});
		},

		// 格式化时间
		formatTime(time) {
			if (!time) return '';
			const date = new Date(time);
			const now = new Date();
			const diff = now - date;

			if (diff < 60000) { // 1分钟内
				return '刚刚';
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前';
			} else if (diff < 86400000) { // 1天内
				return Math.floor(diff / 3600000) + '小时前';
			} else if (diff < 604800000) { // 1周内
				return Math.floor(diff / 86400000) + '天前';
			} else {
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			}
		},

		// ========== 实时访客相关方法 ==========

		// 获取所有节点数据并自行拼装
		async getNodesData() {
			try {
				const response = await listNodes({
					pageSize: 1000000 // 获取所有数据
				});
				if (response.code === 200) {
					const allNodes = response.rows || [];

					// 拼装数据结构
					this.processNodesData(allNodes);
				}
			} catch (error) {
				console.error('获取节点数据失败:', error);
				uni.showToast({
					title: '获取节点数据失败',
					icon: 'none'
				});
			}
		},

		// 处理节点数据，拼装成层级结构
		processNodesData(allNodes) {
			// 创建节点映射表，用于快速查找
			this.allNodesMap = {};
			allNodes.forEach(node => {
				this.allNodesMap[node.id] = node;
			});

			// 筛选出二级节点（市场）- nodeType为'market'
			this.marketList = allNodes.filter(node => node.nodeType === 'market');

			// 为每个市场添加其下级分类（三级节点）
			// 只获取直接子分类，即parentId等于市场ID的分类
			this.marketList.forEach(market => {
				market.categories = allNodes.filter(node => {
					// 必须是分类类型，且父ID是当前市场ID
					if (node.nodeType === 'category' && node.parentId === market.id) {
						// 检查这个分类的父节点是否是市场类型
						const parentNode = allNodes.find(p => p.id === node.parentId);
						return parentNode && parentNode.nodeType === 'market';
					}
					return false;
				});
			});

			console.log('处理后的市场列表:', this.marketList);
			console.log('节点映射表:', this.allNodesMap);

			// 如果有市场数据，默认选中第一个市场
			if (this.marketList.length > 0) {
				this.selectMarket(this.marketList[0]);
			}
		},

		// 选择市场
		selectMarket(market) {
			this.selectedMarket = market;
			this.selectedCategory = {}; // 清空分类选择
			this.visitorList = []; // 清空访客列表

			// 设置该市场下的分类列表
			this.categoryList = market.categories || [];

			// 如果有分类数据，默认选中第一个分类并请求访问记录
			if (this.categoryList.length > 0) {
				this.selectCategory(this.categoryList[0]);
			}
		},

		// 选择分类
		selectCategory(category) {
			this.selectedCategory = category;

			// 获取访问记录
			this.getVisitorList();
		},

		// 获取访问记录列表
		async getVisitorList(loadMore = false) {
			if (this.loadingVisitors) return;
			if (!this.selectedCategory.id) return;

			this.loadingVisitors = true;

			try {
				const params = {
					type: '1', // 固定为1，表示分类访问记录
					targetId: this.selectedCategory.id, // 三级节点ID
					pageNum: loadMore ? this.visitorPageNum : 1,
					pageSize: this.visitorPageSize
				};

				const response = await listSeeLog(params);
				if (response.code === 200) {
					const newVisitors = response.rows || [];

					if (loadMore) {
						this.visitorList = [...this.visitorList, ...newVisitors];
					} else {
						this.visitorList = newVisitors;
						this.visitorPageNum = 1;
					}

					// 判断是否还有更多数据
					this.hasMoreVisitors = newVisitors.length === this.visitorPageSize;
					if (loadMore && newVisitors.length > 0) {
						this.visitorPageNum++;
					}
				}
			} catch (error) {
				console.error('获取访问记录失败:', error);
				uni.showToast({
					title: '获取访问记录失败',
					icon: 'none'
				});
			} finally {
				this.loadingVisitors = false;
			}
		},

		// 加载更多访客
		loadMoreVisitors() {
			if (this.hasMoreVisitors && !this.loadingVisitors) {
				this.getVisitorList(true);
			}
		},

		// 获取访客访问路径
		getVisitorPath(targetId) {
			if (!targetId || !this.allNodesMap[targetId]) {
				return '通过未知路径访问';
			}

			const targetNode = this.allNodesMap[targetId];

			// 如果目标节点是分类，需要找到其所属的市场
			if (targetNode.nodeType === 'category') {
				const marketNode = this.allNodesMap[targetNode.parentId];
				if (marketNode && marketNode.nodeType === 'market') {
					return `通过${marketNode.name}的${targetNode.name}`;
				}
			}

			// 如果目标节点是市场
			if (targetNode.nodeType === 'market') {
				return `通过${targetNode.name}`;
			}

			return `通过${targetNode.name}`;
		},

		// 获取富文本格式的访问路径（带颜色标注）
		getVisitorPathRich(targetId) {
			if (!targetId || !this.allNodesMap[targetId]) {
				return [{
					name: 'span',
					attrs: { style: 'color: #666; font-size: 26rpx;' },
					children: [{ type: 'text', text: '通过未知路径访问' }]
				}];
			}

			const targetNode = this.allNodesMap[targetId];

			// 如果目标节点是分类，需要找到其所属的市场
			if (targetNode.nodeType === 'category') {
				const marketNode = this.allNodesMap[targetNode.parentId];
				if (marketNode && marketNode.nodeType === 'market') {
					return [
						{
							name: 'span',
							attrs: { style: 'color: #666; font-size: 26rpx;' },
							children: [{ type: 'text', text: '通过' }]
						},
						{
							name: 'span',
							attrs: { style: 'color: #ff6b6b; font-size: 26rpx; font-weight: 500;' },
							children: [{ type: 'text', text: marketNode.name }]
						},
						{
							name: 'span',
							attrs: { style: 'color: #666; font-size: 26rpx;' },
							children: [{ type: 'text', text: '的' }]
						},
						{
							name: 'span',
							attrs: { style: 'color: #2979ff; font-size: 26rpx; font-weight: 500;' },
							children: [{ type: 'text', text: targetNode.name }]
						}
					];
				}
			}

			// 如果目标节点是市场
			if (targetNode.nodeType === 'market') {
				return [
					{
						name: 'span',
						attrs: { style: 'color: #666; font-size: 26rpx;' },
						children: [{ type: 'text', text: '通过' }]
					},
					{
						name: 'span',
						attrs: { style: 'color: #ff6b6b; font-size: 26rpx; font-weight: 500;' },
						children: [{ type: 'text', text: targetNode.name }]
					}
				];
			}

			return [{
				name: 'span',
				attrs: { style: 'color: #666; font-size: 26rpx;' },
				children: [{ type: 'text', text: `通过${targetNode.name}` }]
			}];
		},

		// ========== 搜索访客相关方法 ==========

		// 获取搜索记录列表
		async getSearchLogList(isLoadMore = false) {
			if (this.searchLoading) return;

			// 如果不是加载更多，重置分页参数
			if (!isLoadMore) {
				this.searchPageNum = 1;
				this.searchHasMore = true;
				this.searchLogList = [];
			}

			this.searchLoading = true;
			this.searchLogExecuted = true;

			try {
				const params = {
					pageNum: this.searchPageNum,
					pageSize: this.searchPageSize
				};

				const response = await listSearchLog(params);

				if (response.code === 200) {
					const newLogs = response.rows || [];

					if (isLoadMore) {
						this.searchLogList = [...this.searchLogList, ...newLogs];
					} else {
						this.searchLogList = newLogs;
					}

					// 判断是否还有更多数据
					this.searchHasMore = newLogs.length >= this.searchPageSize;

					if (this.searchHasMore) {
						this.searchPageNum++;
					}
				} else {
					uni.showToast({
						title: response.msg || '获取搜索记录失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取搜索记录失败:', error);
				uni.showToast({
					title: '获取搜索记录失败',
					icon: 'none'
				});
			} finally {
				this.searchLoading = false;
				// 不在这里设置 searchRefreshing = false，由调用方控制
			}
		},

		// 加载更多搜索记录
		loadMoreSearchLogs() {
			if (this.searchHasMore && !this.searchLoading) {
				this.getSearchLogList(true);
			}
		},

		// 下拉刷新搜索记录
		async onSearchRefresh() {
			try {
				this.searchRefreshing = true;
				this.searchPageNum = 1;
				this.searchHasMore = true;
				this.searchLogList = []; // 清空现有数据
				await this.getSearchLogList();
			} catch (error) {
				console.error('刷新搜索记录失败:', error);
			} finally {
				this.searchRefreshing = false;
			}
		},

		// 下拉刷新访客记录
		async onVisitorRefresh() {
			try {
				this.visitorRefreshing = true;
				this.visitorPageNum = 1;
				this.hasMoreVisitors = true;
				this.visitorList = []; // 清空现有数据
				await this.getVisitorList();
			} catch (error) {
				console.error('刷新访客记录失败:', error);
			} finally {
				this.visitorRefreshing = false;
			}
		},

		// 下拉刷新易购圈
		async onCircleRefresh() {
			try {
				this.circleRefreshing = true;
				this.pageNum = 1;
				this.hasMore = true;
				this.postList = []; // 清空现有数据
				await this.getPostList();
			} catch (error) {
				console.error('刷新易购圈失败:', error);
			} finally {
				this.circleRefreshing = false;
			}
		},

		// 获取用户名称
		getUserName(nickName, userId) {
			if (nickName) {
				return nickName;
			}
			return userId === 0 ? '匿名用户' : '微信用户';
		},

		// tabBar切换事件
		onTabChange(e) {
			console.log('Tab切换到:', e.index, e.pagePath);
		}
	}
}
</script>

<style lang="scss">
page {
	height: 100%;
	background-color: #f5f5f5;
}

.work-container {
	min-height: 100vh;
	background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
	background-attachment: fixed;
	background-repeat: no-repeat;
	padding-bottom: 120rpx; /* 为自定义tabBar预留空间 */
}

.tabs-container {
	background-color: #fff;
	border-bottom: 1px solid #e5e5e5;

	.tabs-wrapper {
		display: flex;
		align-items: center;

		.tab-item {
			flex: 1;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			.tab-text {
				font-size: 28rpx;
				color: #666;
				transition: color 0.3s;
			}

			&.active {
				.tab-text {
					color: #2979ff;
					font-weight: 500;
				}

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 4rpx;
					background-color: #2979ff;
					border-radius: 2rpx;
				}
			}
		}
	}
}

.content-container {
	flex: 1;

	.tab-content {
		padding: 20rpx;

		.placeholder-content {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 400rpx;
			background-color: #fff;
			border-radius: 12rpx;

			text {
				color: #999;
				font-size: 28rpx;
			}
		}

		&.circle-tab {
			padding: 0;
			display: flex;
			flex-direction: column;
			height: calc(100vh - 88rpx - 40rpx);
		}

		&.visitor-tab {
			padding: 0;
			display: flex;
			flex-direction: column;
			height: calc(100vh - 88rpx - 40rpx);
		}
	}
}

// 易购圈相关样式
.circle-list-container {
	background-color: #fff;
	padding: 20rpx 0;
	border-bottom: 1px solid #f0f0f0;

	.circle-scroll {
		white-space: nowrap;

		.circle-list {
			display: flex;
			padding: 0 20rpx;

			.circle-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-right: 30rpx;
				padding: 15rpx;
				border-radius: 12rpx;
				transition: all 0.3s;
				min-width: 140rpx;

				&:last-child {
					margin-right: 20rpx;
				}

				&.active {
					background-color: #f0f8ff;

					.circle-name {
						color: #2979ff;
						font-weight: 500;
					}
				}

				.circle-avatar {
					width: 100rpx;
					height: 100rpx;
					border-radius: 50rpx;
					margin-bottom: 12rpx;

					&.default-avatar {
						background-color: #2979ff;
						display: flex;
						align-items: center;
						justify-content: center;

						.avatar-text {
							color: #fff;
							font-size: 36rpx;
							font-weight: 500;
						}
					}
				}

				.circle-name {
					font-size: 26rpx;
					color: #333;
					text-align: center;
					width: 140rpx;
					word-wrap: break-word;
					word-break: break-all;
					line-height: 1.3;
					max-height: 68rpx;
					overflow: hidden;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
				}
			}
		}
	}
}

.posts-container {
	flex: 1;
	padding: 0;
	display: flex;
	flex-direction: column;
	overflow: hidden;

	.posts-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		margin: 20rpx 20rpx 20rpx 20rpx;
	}

	.posts-scroll {
		flex: 1;
		height: calc(100vh - 300rpx);
		padding: 0 20rpx 20rpx 20rpx;

		.posts-list {
			.post-item {
				background-color: #fff;
				border-radius: 12rpx;
				padding: 24rpx;
				margin-bottom: 20rpx;

				.post-header {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					.user-avatar {
						width: 80rpx;
						height: 80rpx;
						border-radius: 40rpx;
						margin-right: 20rpx;
					}

					.user-info {
						flex: 1;

						.user-name {
							display: block;
							font-size: 28rpx;
							font-weight: 500;
							color: #333;
							margin-bottom: 8rpx;
						}

						.post-time {
							font-size: 24rpx;
							color: #999;
						}
					}
				}

				.post-content {
					margin-bottom: 20rpx;

					.content-text {
						font-size: 28rpx;
						color: #333;
						line-height: 1.6;
					}
				}

				.post-images {
					margin-bottom: 20rpx;

					.image-grid {
						display: flex;
						flex-wrap: wrap;
						gap: 8rpx;

						.grid-image {
							border-radius: 8rpx;

							&.single {
								width: 400rpx;
								height: 300rpx;
							}

							&.double {
								width: calc(50% - 4rpx);
								height: 200rpx;
							}

							&.four {
								width: calc(50% - 4rpx);
								height: 150rpx;
							}

							&.multiple {
								width: calc(33.333% - 6rpx);
								height: 150rpx;
							}
						}
					}
				}

				.post-video {
					margin-bottom: 20rpx;

					.video-player {
						width: 100%;
						height: 400rpx;
						border-radius: 8rpx;
					}
				}
			}

			.load-more, .no-more, .empty-posts {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;

				text {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
}

.publish-btn {
	position: fixed;
	right: 30rpx;
	bottom: 100rpx;
	width: 100rpx;
	height: 100rpx;
	background-color: #2979ff;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.3);
	z-index: 999;

	.publish-icon {
		color: #fff;
		font-size: 48rpx;
		font-weight: 300;
		line-height: 1;
	}
}

// 实时访客相关样式
.filter-container {
	background-color: #fff;
	padding: 20rpx;
	border-bottom: 1px solid #f0f0f0;

	.filter-section {
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.filter-header {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;

			.filter-icon {
				font-size: 28rpx;
				margin-right: 12rpx;
			}

			.filter-title {
				font-size: 28rpx;
				font-weight: 500;
				color: #333;
			}
		}

		.filter-scroll {
			white-space: nowrap;

			.filter-tags {
				display: flex;
				padding: 0 4rpx;

				.filter-tag {
					display: inline-flex;
					align-items: center;
					padding: 12rpx 24rpx;
					margin-right: 16rpx;
					background-color: #f8f9fa;
					border: 1px solid #e5e5e5;
					border-radius: 20rpx;
					white-space: nowrap;
					transition: all 0.3s;

					&:last-child {
						margin-right: 20rpx;
					}

					&.active {
						background-color: #ff4757;
						border-color: #ff4757;

						.tag-text {
							color: #fff;
							font-weight: 500;
						}
					}

					.tag-text {
						font-size: 26rpx;
						color: #333;
					}
				}
			}
		}
	}
}

.visitor-list-container {
	flex: 1;
	padding: 0;
	overflow: hidden;

	.visitor-scroll {
		height: calc(100vh - 300rpx);
		padding: 20rpx;

		.visitor-list {
			.visitor-item {
				position: relative;
				display: flex;
				align-items: flex-start;
				padding: 24rpx 24rpx 24rpx 80rpx;
				background-color: transparent;
				margin-bottom: 0;
				overflow: hidden;

				// 时间轴竖线
				.timeline-line {
					position: absolute;
					left: 40rpx;
					top: 80rpx;
					bottom: -16rpx;
					width: 2rpx;
					background-color: #e8e8e8;
				}

				// 时间轴圆点
				.timeline-dot {
					position: absolute;
					left: 32rpx;
					top: 72rpx;
					width: 18rpx;
					height: 18rpx;
					background-color: #ff6b6b;
					border-radius: 50%;
					border: 3rpx solid #fff;
					box-shadow: 0 0 0 1rpx #e8e8e8;
				}

				// 最后一个项目不显示竖线
				&:last-child {
					.timeline-line {
						display: none;
					}
				}

				.visitor-avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 40rpx;
					margin-right: 20rpx;
					margin-top: 8rpx;
					flex-shrink: 0; // 防止头像被压缩
				}

				.visitor-info {
					flex: 1;
					padding: 16rpx 0;
					min-width: 0; // 允许flex子项收缩
					overflow: hidden;

					.visitor-name {
						display: block;
						font-size: 28rpx;
						font-weight: 500;
						color: #333;
						margin-bottom: 8rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.visitor-path {
						display: block;
						font-size: 26rpx;
						color: #666;
						margin-bottom: 8rpx;
						line-height: 1.4;
						word-wrap: break-word;
						word-break: break-all;
					}

					.visitor-time {
						font-size: 24rpx;
						color: #999;
					}
				}
			}

			.load-more, .no-more, .empty-visitors {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;

				text {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
}

// 搜索访客样式
.search-tab {
	.search-log-list {
		height: calc(100vh - 200rpx);
		padding: 20rpx;
	}

	.search-log-item {
		position: relative;
		padding: 20rpx 20rpx 20rpx 80rpx;
		margin-bottom: 20rpx;

		// 时间轴竖线
		.timeline-line {
			position: absolute;
			left: 40rpx;
			top: 87rpx;
			bottom: -20rpx;
			width: 2rpx;
			background-color: #e8e8e8;
		}

		// 时间轴圆点
		.timeline-dot {
			position: absolute;
			left: 32rpx;
			top: 78rpx;
			width: 18rpx;
			height: 18rpx;
			background-color: #DD1A21;
			border-radius: 50%;
			border: 3rpx solid #f5f5f5;
			box-shadow: 0 0 0 1rpx #e8e8e8;
		}

		// 第一个项目的竖线从圆点开始
		&:first-child {
			.timeline-line {
				top: 87rpx;
			}
		}

		// 最后一个项目不显示竖线
		&:last-child {
			.timeline-line {
				display: none;
			}
		}

		.log-header {
			display: flex;
			align-items: flex-start;

			.user-avatar {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50rpx;
				margin-right: 20rpx;
				margin-top: 8rpx;
				flex-shrink: 0;
			}

			.log-content {
				flex: 1;
				padding: 16rpx 0;

				.search-text {
					margin-bottom: 12rpx;
					line-height: 1.6;

					.user-name {
						font-size: 32rpx;
						color: #333;
						font-weight: bold;
					}

					.normal-text {
						font-size: 32rpx;
						color: #333;
					}

					.search-label {
						font-size: 32rpx;
						color: #DD1A21;
						font-weight: bold;
					}

					.search-separator {
						font-size: 32rpx;
						color: #999;
					}

					.search-action {
						font-size: 32rpx;
						color: #333;
					}

					.search-keyword {
						font-size: 32rpx;
						color: #DD1A21;
						font-weight: bold;
					}
				}

				.log-meta {
					.log-time {
						font-size: 28rpx;
						color: #999;
					}
				}
			}
		}
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;

		.empty-emoji {
			font-size: 80rpx;
			margin-bottom: 20rpx;
		}

		.empty-text {
			font-size: 28rpx;
			color: #999;
		}
	}

	.loading-more {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx 0;

		.loading-text {
			font-size: 26rpx;
			color: #999;
			margin-left: 10rpx;
		}
	}

	.no-more {
		display: flex;
		justify-content: center;
		padding: 30rpx 0;

		.no-more-text {
			font-size: 26rpx;
			color: #ccc;
		}
	}
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}
</style>
