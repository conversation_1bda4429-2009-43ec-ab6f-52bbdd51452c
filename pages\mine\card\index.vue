<template>
	<view class="card-container">
		<!-- 步骤1: 基本信息 -->
		<view v-if="currentStep === 1" class="step-content">
			<view class="form-container">
				<view class="form-item">
					<text class="label required">姓名</text>
					<view class="input-wrapper">
						<input
							class="input"
							v-model="formData.realName"
							placeholder="请输入您的真实姓名"
							maxlength="30"
						/>
					</view>
				</view>

				<view class="form-item">
					<text class="label required">电话</text>
					<view class="input-wrapper">
						<input
							class="input"
							v-model="formData.phone"
							placeholder="请输入联系电话"
							type="number"
							maxlength="20"
						/>
					</view>
				</view>

				<view class="form-item">
					<text class="label required">店铺名称/公司名称</text>
					<view class="input-wrapper">
						<input
							class="input"
							v-model="formData.companyName"
							placeholder="请输入店铺或公司名称"
							maxlength="100"
						/>
					</view>
				</view>

				<view class="form-item">
					<text class="label required">地区</text>
					<picker
						mode="region"
						:value="regionArray"
						@change="onRegionChange"
						class="region-picker-native"
					>
						<view class="region-picker">
							<text class="region-text" :class="{ placeholder: !formData.region }">
								{{ formData.region || '请选择省市区' }}
							</text>
							<uni-icons type="right" size="16" color="#C8C7CC"></uni-icons>
						</view>
					</picker>
				</view>

				<view class="form-item">
					<text class="label required">详细地址</text>
					<textarea 
						class="textarea" 
						v-model="formData.address" 
						placeholder="请输入详细地址"
						maxlength="255"
						auto-height
					/>
				</view>
			</view>

			<!-- 用户协议 -->
			<view class="agreement-section">
				<view class="agreement-checkbox" @click="toggleAgreement">
					<uni-icons 
						:type="agreed ? 'checkbox-filled' : 'circle'" 
						:color="agreed ? '#DD1A21' : '#C8C7CC'" 
						size="18"
					></uni-icons>
					<text class="agreement-text">
						请先同意 
						<text class="link" @click.stop="showUserAgreement">《用户协议》</text> 
						和 
						<text class="link" @click.stop="showPrivacyPolicy">《隐私政策》</text>
					</text>
				</view>
			</view>
		</view>

		<!-- 步骤2: 用户类型选择 -->
		<view v-if="currentStep === 2" class="step-content">
			<view class="tip-section">
				<text class="tip-text">请点击选择 您是做什么的？干什么的？干万不要选错，选错会错失很多商机！</text>
			</view>

			<view class="user-type-grid">
				<view 
					v-for="(type, index) in userTypes" 
					:key="index"
					class="type-item"
					:class="{ active: formData.userType === type.value }"
					@click="selectUserType(type.value)"
				>
					<text class="type-emoji">{{ type.emoji }}</text>
					<text class="type-text">{{ type.label }}</text>
				</view>
			</view>
		</view>

		<!-- 步骤3: 行业选择 -->
		<view v-if="currentStep === 3" class="step-content">
			<view class="industry-section">
				<text class="section-title">行业</text>
				<view class="industry-grid">
					<view 
						v-for="(industry, index) in industries" 
						:key="index"
						class="industry-item"
						:class="{ active: selectedIndustries.includes(industry) }"
						@click="toggleIndustry(industry)"
					>
						<text class="industry-text">{{ industry }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="bottom-button">
			<view class="button-row" v-if="currentStep > 1">
				<button class="back-btn" @click="handleBack">
					返回上一步
				</button>
				<button
					class="next-btn"
					:class="{ disabled: !canProceed }"
					@click="handleNext"
				>
					{{ currentStep === 3 ? '完成' : `下一步 (${currentStep}/3)` }}
				</button>
			</view>
			<button
				v-else
				class="next-btn full-width"
				:class="{ disabled: !canProceed }"
				@click="handleNext"
			>
				{{ currentStep === 3 ? '完成' : `下一步 (${currentStep}/3)` }}
			</button>
		</view>


	</view>
</template>

<script>
import { listUserCard, addUserCard, updateUserCard } from "@/api/buy/userCard.js"

export default {
	data() {
		return {
			currentStep: 1,
			agreed: false,
			regionArray: [],
			existingCardId: null, // 现有名片ID
			
			// 表单数据
			formData: {
				realName: '',
				phone: '',
				companyName: '',
				region: '',
				address: '',
				userType: '',
				industrys: ''
			},

			// 用户类型选项
			userTypes: [
				{ label: '工程采购', value: '工程采购', emoji: '🏗️' },
				{ label: '零售商店', value: '零售商店', emoji: '🏪' },
				{ label: '单位采购', value: '单位采购', emoji: '🏢' },
				{ label: '生产厂家', value: '生产厂家', emoji: '🏭' },
				{ label: '装饰公司', value: '装饰公司', emoji: '🎨' },
				{ label: '总代批发', value: '总代批发', emoji: '📦' },
				{ label: '物流货运', value: '物流货运', emoji: '🚚' },
				{ label: '其他行业', value: '其他行业', emoji: '🔧' },
				{ label: '个人用户', value: '个人用户', emoji: '👤' }
			],

			// 行业选项
			industries: [
				'五金机电', '建筑材料', '建筑配件', '消防安防', '电线电缆', '电气电料',
				'水暖管件', '家装装饰', '灯饰照明', '钢材建材', '工程装饰', '劳保安防',
				'农资日杂', '园林石材', '农机汽配', '物流货站'
			],

			// 选中的行业
			selectedIndustries: []
		}
	},

	computed: {
		canProceed() {
			if (this.currentStep === 1) {
				return this.formData.realName && 
					   this.formData.phone && 
					   this.formData.companyName && 
					   this.formData.region && 
					   this.formData.address && 
					   this.agreed;
			} else if (this.currentStep === 2) {
				return this.formData.userType;
			} else if (this.currentStep === 3) {
				return this.selectedIndustries.length > 0 && this.selectedIndustries.length <= 6;
			}
			return false;
		}
	},

	onLoad() {
		this.checkExistingCard();
	},

	methods: {
		// 检查是否已有名片
		async checkExistingCard() {
			try {
				const userId = this.$store.state.user.userId;
				if (!userId) {
					this.$modal.msgError('请先登录');
					return;
				}

				const response = await listUserCard({ userId });
				if (response.code === 200 && response.rows && response.rows.length > 0) {
					// 已有名片，填充数据
					const card = response.rows[0];
					this.existingCardId = card.id;
					this.formData = {
						realName: card.realName || '',
						phone: card.phone || '',
						companyName: card.companyName || '',
						region: card.region || '',
						address: card.address || '',
						userType: card.userType || '',
						industrys: card.industrys || ''
					};

					// 解析已选行业
					if (card.industrys) {
						this.selectedIndustries = card.industrys.split(',').filter(item => item.trim());
					}

					// 解析地区数据
					if (card.region) {
						this.regionArray = card.region.split('-');
					}

					this.agreed = true;
				}
			} catch (error) {
				console.error('获取名片信息失败:', error);
			}
		},

		// 切换协议同意状态
		toggleAgreement() {
			this.agreed = !this.agreed;
		},

		// 显示用户协议
		showUserAgreement() {
			uni.navigateTo({
				url: '/pages/userAgrement/userAgrement'
			});
		},

		// 显示隐私政策
		showPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/privacy/privacy'
			});
		},

		// 地区选择变化
		onRegionChange(e) {
			const [province, city, area] = e.detail.value;
			this.regionArray = [province, city, area];
			this.formData.region = `${province}-${city}-${area}`;
		},

		// 选择用户类型
		selectUserType(type) {
			this.formData.userType = type;
		},

		// 切换行业选择
		toggleIndustry(industry) {
			const index = this.selectedIndustries.indexOf(industry);
			if (index > -1) {
				this.selectedIndustries.splice(index, 1);
			} else {
				if (this.selectedIndustries.length < 6) {
					this.selectedIndustries.push(industry);
				} else {
					this.$modal.msgError('最多只能选择6个行业');
				}
			}
		},

		// 处理下一步
		handleNext() {
			if (!this.canProceed) return;

			if (this.currentStep < 3) {
				this.currentStep++;
			} else {
				this.submitCard();
			}
		},

		// 返回上一步
		handleBack() {
			if (this.currentStep > 1) {
				this.currentStep--;
			}
		},

		// 提交名片
		async submitCard() {
			try {
				this.$modal.loading('提交中...');

				const userId = this.$store.state.user.userId;
				const cardData = {
					...this.formData,
					userId,
					industrys: this.selectedIndustries.join(',')
				};

				let response;
				if (this.existingCardId) {
					// 更新现有名片
					cardData.id = this.existingCardId;
					response = await updateUserCard(cardData);
				} else {
					// 创建新名片
					response = await addUserCard(cardData);
				}

				this.$modal.closeLoading();

				if (response.code === 200) {
					this.$modal.msgSuccess(this.existingCardId ? '名片更新成功' : '名片创建成功');
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					this.$modal.msgError(response.msg || '操作失败');
				}
			} catch (error) {
				this.$modal.closeLoading();
				console.error('提交名片失败:', error);
				this.$modal.msgError('操作失败，请稍后重试');
			}
		}
	}
}
</script>

<style lang="scss">
.card-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

.step-content {
	padding: 40rpx 30rpx;
}

// 表单样式
.form-container {
	.form-item {
		margin-bottom: 40rpx;

		.label {
			display: block;
			font-size: 28rpx;
			color: #333;
			margin-bottom: 16rpx;
			font-weight: 500;

			&.required::before {
				content: '*';
				color: #DD1A21;
				margin-right: 8rpx;
			}
		}

		.input-wrapper {
			width: 100%;
			height: 88rpx;
			border: 1px solid #e5e5e5;
			border-radius: 12rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			padding: 0 24rpx;
			box-sizing: border-box;

			.input {
				width: 100%;
				height: 100%;
				border: none;
				background: transparent;
				font-size: 28rpx;
				color: #333;
				outline: none;
			}
		}

		.textarea {
			width: 100%;
			padding: 24rpx;
			border: 1px solid #e5e5e5;
			border-radius: 12rpx;
			font-size: 28rpx;
			background-color: #fff;
			box-sizing: border-box;
			min-height: 120rpx;
			resize: none;
		}



		.region-picker-native {
			width: 100%;
		}

		.region-picker {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx;
			border: 1px solid #e5e5e5;
			border-radius: 12rpx;
			background-color: #fff;

			.region-text {
				font-size: 28rpx;
				color: #333;

				&.placeholder {
					color: #999;
				}
			}
		}
	}
}

// 协议样式
.agreement-section {
	margin-top: 40rpx;

	.agreement-checkbox {
		display: flex;
		align-items: flex-start;

		.agreement-text {
			margin-left: 12rpx;
			font-size: 24rpx;
			color: #666;
			line-height: 1.5;

			.link {
				color: #2979ff;
			}
		}
	}
}

// 提示样式
.tip-section {
	background-color: #fff2f2;
	border: 1px solid #ffccc7;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 40rpx;

	.tip-text {
		font-size: 26rpx;
		color: #DD1A21;
		line-height: 1.6;
	}
}

// 用户类型网格
.user-type-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 24rpx;

	.type-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 32rpx 16rpx;
		background-color: #fff;
		border: 2px solid #e5e5e5;
		border-radius: 16rpx;
		transition: all 0.3s;

		&.active {
			border-color: #DD1A21;
			background-color: #fff2f2;
		}

		.type-emoji {
			font-size: 48rpx;
			margin-bottom: 16rpx;
		}

		.type-text {
			font-size: 26rpx;
			color: #333;
			text-align: center;
		}
	}
}

// 行业选择样式
.industry-section {
	.section-title {
		display: block;
		font-size: 32rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 32rpx;

		&::before {
			content: '*';
			color: #DD1A21;
			margin-right: 8rpx;
		}
	}

	.industry-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;

		.industry-item {
			padding: 16rpx 24rpx;
			background-color: #f8f9fa;
			border: 1px solid #e5e5e5;
			border-radius: 24rpx;
			transition: all 0.3s;

			&.active {
				background-color: #DD1A21;
				border-color: #DD1A21;

				.industry-text {
					color: #fff;
				}
			}

			.industry-text {
				font-size: 26rpx;
				color: #333;
			}
		}
	}
}

// 底部按钮
.bottom-button {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 24rpx 30rpx;
	background-color: #fff;
	border-top: 1px solid #e5e5e5;

	.button-row {
		display: flex;
		gap: 20rpx;

		.back-btn {
			flex: 1;
			height: 88rpx;
			background-color: #f8f9fa;
			color: #666;
			border: 1px solid #e5e5e5;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: 600;
		}

		.next-btn {
			flex: 2;
			height: 88rpx;
			background-color: #DD1A21;
			color: #fff;
			border: none;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: 600;

			&.disabled {
				background-color: #ccc;
				color: #999;
			}
		}
	}

	.next-btn.full-width {
		width: 100%;
		height: 88rpx;
		background-color: #DD1A21;
		color: #fff;
		border: none;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: 600;

		&.disabled {
			background-color: #ccc;
			color: #999;
		}
	}
}
</style>
