import request from '@/utils/request'

// 查询用户积分流水日志列表
export function listPointsLog(query) {
  return request({
    url: '/buy/pointsLog/list',
    method: 'get',
    params: query
  })
}

// 查询用户积分流水日志详细
export function getPointsLog(logId) {
  return request({
    url: '/buy/pointsLog/' + logId,
    method: 'get'
  })
}

// 新增用户积分流水日志
export function addPointsLog(data) {
  return request({
    url: '/buy/pointsLog',
    method: 'post',
    data: data
  })
}

// 修改用户积分流水日志
export function updatePointsLog(data) {
  return request({
    url: '/buy/pointsLog',
    method: 'put',
    data: data
  })
}

// 删除用户积分流水日志
export function delPointsLog(logId) {
  return request({
    url: '/buy/pointsLog/' + logId,
    method: 'delete'
  })
}
