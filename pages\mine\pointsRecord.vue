<template>
	<scroll-view 
		class="points-record-container" 
		scroll-y="true" 
		:refresher-enabled="true"
		:refresher-triggered="refreshing"
		@refresherrefresh="onRefresh"
		@scrolltolower="loadMore"
	>
		<!-- 积分记录列表 -->
		<view class="record-list">
			<view
				v-for="record in recordList"
				:key="record.id"
				class="record-item"
			>
				<view class="record-content">
					<view class="record-info">
						<text class="record-desc">{{ record.description }}</text>
						<text class="record-time">{{ formatTime(record.createTime) }}</text>
					</view>
					<view class="record-points" :class="{ 'positive': record.pointsChange > 0, 'negative': record.pointsChange < 0 }">
						<text class="points-text">{{ record.pointsChange > 0 ? '+' : '' }}{{ record.pointsChange }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading && recordList.length > 0" class="loading-more">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 没有更多数据 -->
		<view v-if="!hasMore && recordList.length > 0" class="no-more">
			<text class="no-more-text">没有更多记录了</text>
		</view>

		<!-- 空状态 -->
		<view v-if="!loading && recordList.length === 0" class="empty-state">
			<text class="empty-icon">📝</text>
			<text class="empty-text">暂无积分记录</text>
			<text class="empty-desc">完成任务获得积分后会在这里显示</text>
		</view>
	</scroll-view>
</template>

<script>
import { listPointsLog } from '@/api/buy/pointsLog.js'

export default {
	data() {
		return {
			recordList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			pageNum: 1,
			pageSize: 20
		}
	},
	onLoad() {
		this.checkLoginAndGetData()
	},
	onShow() {
		// 页面显示时刷新数据
		this.checkLoginAndGetData()
	},
	methods: {
		// 检查登录状态并获取数据
		checkLoginAndGetData() {
			// 检查用户是否登录
			if (!this.$store.state.user || !this.$store.state.user.userId) {
				uni.showModal({
					title: '提示',
					content: '请先登录后查看积分记录',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login'
							})
						} else {
							uni.navigateBack()
						}
					}
				})
				return
			}
			this.getPointsRecordList()
		},

		// 获取积分记录列表
		async getPointsRecordList(loadMore = false) {
			if (this.loading) return

			this.loading = true

			try {
				const params = {
					userId: this.$store.state.user.userId,
					pageNum: loadMore ? this.pageNum : 1,
					pageSize: this.pageSize
				}

				const response = await listPointsLog(params)

				if (response.code === 200) {
					const newRecords = response.rows || []

					if (loadMore) {
						this.recordList = [...this.recordList, ...newRecords]
					} else {
						this.recordList = newRecords
						this.pageNum = 1
					}

					// 判断是否还有更多数据
					this.hasMore = newRecords.length === this.pageSize
					if (loadMore && newRecords.length > 0) {
						this.pageNum++
					}
				} else {
					uni.showToast({
						title: response.msg || '获取积分记录失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('获取积分记录失败:', error)
				uni.showToast({
					title: '获取积分记录失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				this.refreshing = false
			}
		},

		// 下拉刷新
		async onRefresh() {
			this.refreshing = true
			this.pageNum = 1
			this.hasMore = true
			await this.getPointsRecordList()
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.getPointsRecordList(true)
			}
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return ''
			
			const date = new Date(timeStr)
			const now = new Date()
			const diff = now - date
			
			// 一天内显示时间
			if (diff < 24 * 60 * 60 * 1000) {
				const hours = date.getHours().toString().padStart(2, '0')
				const minutes = date.getMinutes().toString().padStart(2, '0')
				return `今天 ${hours}:${minutes}`
			}
			
			// 一年内显示月日
			if (date.getFullYear() === now.getFullYear()) {
				const month = (date.getMonth() + 1).toString().padStart(2, '0')
				const day = date.getDate().toString().padStart(2, '0')
				return `${month}-${day}`
			}
			
			// 超过一年显示年月日
			const year = date.getFullYear()
			const month = (date.getMonth() + 1).toString().padStart(2, '0')
			const day = date.getDate().toString().padStart(2, '0')
			return `${year}-${month}-${day}`
		}
	}
}
</script>

<style lang="scss" scoped>
.points-record-container {
	height: 100vh;
	background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
	padding: 20rpx;
	box-sizing: border-box;
}

.record-list {
	.record-item {
		background: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;

		.record-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;

			.record-info {
				flex: 1;
				margin-right: 20rpx;

				.record-desc {
					display: block;
					font-size: 28rpx;
					font-weight: 500;
					color: #333;
					margin-bottom: 8rpx;
					line-height: 1.4;
				}

				.record-time {
					font-size: 24rpx;
					color: #999;
				}
			}

			.record-points {
				&.positive {
					.points-text {
						color: #DD1A21;
						font-weight: bold;
					}
				}

				&.negative {
					.points-text {
						color: #666;
						font-weight: bold;
					}
				}

				.points-text {
					font-size: 32rpx;
					font-weight: 600;
				}
			}
		}
	}
}

.loading-more, .no-more {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 80rpx;
	margin: 20rpx 0;

	.loading-text, .no-more-text {
		font-size: 24rpx;
		color: #999;
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;

	.empty-icon {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}

	.empty-text {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 12rpx;
	}

	.empty-desc {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
	}
}
</style>
