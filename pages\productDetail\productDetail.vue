<template>
	<view class="page-container">
		<view v-if="productInfo" class="content-wrapper">
			<!-- 商品名称 -->
			<view class="product-header card-section">
				<text class="title">{{ productInfo.productName }}</text>
			</view>
			
			<u-gap height="10" bgColor="#f5f5f5"></u-gap>

			<!-- 商品图片展示 -->
			<view class="product-images card-section" v-if="productImages.length > 0">
				<view 
					v-for="(image, index) in productImages" 
					:key="index" 
					class="image-item"
				>
					<image 
						:src="image" 
						class="product-image" 
						mode="aspectFill" 
						@click="previewImage(image, productImages)"
					></image>
				</view>
			</view>

		</view>
		
		<view v-else class="loading-container">
			<u-loading-icon text="加载中..." textSize="16"></u-loading-icon>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-bar" v-if="productInfo">
			<view class="icon-group">
				<view class="icon-item" @click="handleFavorite">
					<u-icon name="star" size="22"></u-icon>
					<text>收藏</text>
				</view>
				<view class="icon-item" @click="handleShare">
					<u-icon name="share" size="22"></u-icon>
					<text>分享</text>
				</view>
			</view>
			<view class="button-group">
				<view class="action-btn wechat-btn" @click="contactViaWechat">
					<u-icon name="weixin-fill" color="#fff" size="20"></u-icon>
					<text>微信</text>
				</view>
				<view class="action-btn call-btn" @click="callPhone">
					<u-icon name="phone-fill" color="#fff" size="20"></u-icon>
					<text>电话</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getProduct } from "@/api/buy/product.js"
	import { getStoreInfo } from "@/api/buy/storeInfo.js"
	import { addSeeLog } from "@/api/buy/seeLog.js"
	import { baseUrl } from "../../config";
	
	export default {
		data() {
			return {
				productId: '',
				productInfo: null,
				storeInfo: null,
				baseUrl
			}
		},
		onLoad(options) {
			if (options.id) {
				this.productId = options.id;
				this.getProductDetails();
			} else {
				this.$modal.msgError("缺少商品ID");
			}
		},
		computed: {
			productImages() {
				if (!this.productInfo || !this.productInfo.productImages) return [];
				return this.productInfo.productImages.split(',').filter(url => url.trim()).map(url => this.getFullImageUrl(url));
			},
			phoneNumbers() {
				if (!this.storeInfo || !this.storeInfo.phone) return [];
				return this.storeInfo.phone.split(',').filter(phone => phone.trim());
			}
		},
		methods: {
			// 获取完整的图片URL
			getFullImageUrl(url) {
				if (!url) return '';
				// 如果已经是完整的URL，直接返回
				if (url.startsWith('http://') || url.startsWith('https://')) {
					return url;
				}
				// 否则拼接baseUrl
				return this.baseUrl + url;
			},

			getProductDetails() {
				this.$modal.loading("加载中...");
				getProduct(this.productId).then(res => {
					if (res.code === 200 && res.data) {
						this.productInfo = res.data;
						uni.setNavigationBarTitle({ title: this.productInfo.productName || '商品详情' });
						// 记录店铺访问记录（通过商品访问）
						if (this.productInfo.storeId) {
							this.recordStoreVisit(this.productInfo.storeId);
						}
						// 获取店铺信息
						this.getStoreDetails();
					} else {
						this.$modal.closeLoading();
						this.$modal.msgError(res.msg || '商品信息不存在');
					}
				}).catch(error => {
					this.$modal.closeLoading();
					console.error('获取商品详情失败:', error);
					this.$modal.msgError("加载失败，请稍后重试");
				});
			},

			// 获取店铺信息（用于联系功能）
			getStoreDetails() {
				if (!this.productInfo.storeId) {
					this.$modal.closeLoading();
					return;
				}
				
				getStoreInfo(this.productInfo.storeId).then(res => {
					this.$modal.closeLoading();
					if (res.code === 200 && res.data) {
						this.storeInfo = res.data;
					}
				}).catch(error => {
					this.$modal.closeLoading();
					console.error('获取店铺信息失败:', error);
				});
			},
			
			previewImage(current, urls) {
				uni.previewImage({ current, urls });
			},
			
			callPhone() {
				if (!this.phoneNumbers.length) {
					this.$modal.msg("暂无电话信息");
					return;
				}
				uni.makePhoneCall({ phoneNumber: this.phoneNumbers[0] });
			},
			
			handleFavorite() {
				this.$modal.msgSuccess('收藏成功');
			},
			
			handleShare() {
				this.$modal.msg('分享功能待实现');
			},
			
			contactViaWechat() {
				if(!this.storeInfo || !this.storeInfo.wechatQrcode){
					this.$modal.msg("暂无微信二维码");
					return;
				}
				this.previewImage(this.getFullImageUrl(this.storeInfo.wechatQrcode), [this.getFullImageUrl(this.storeInfo.wechatQrcode)]);
			},

			// 记录店铺访问记录
			recordStoreVisit(storeId) {
				try {
					// 获取用户ID，如果未登录则为0
					let userId = 0;
					if (this.$store.state.user && this.$store.state.user.userId) {
						userId = this.$store.state.user.userId;
					}

					const visitData = {
						userId: userId,
						targetId: storeId,
						type: '0' // 店铺访问记录
					};

					// 异步记录访问，不影响页面加载
					addSeeLog(visitData).then(res => {
						console.log('店铺访问记录已记录（通过商品）:', res);
					}).catch(error => {
						console.error('记录店铺访问失败:', error);
					});
				} catch (error) {
					console.error('记录访问记录异常:', error);
				}
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f5f5f5;
		padding-bottom: 120rpx; 
	}
	.content-wrapper {
		padding: 20rpx;
	}

	.card-section {
		background-color: #fff;
		padding: 24rpx;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
	}
	
	.product-header {
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #303133;
			display: block;
			line-height: 1.4;
		}
	}

	.product-images {
		padding: 0;
		
		.image-item {
			margin-bottom: 20rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.product-image {
				width: 100%;
				height: 600rpx;
				border-radius: 12rpx;
			}
		}
	}

	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80vh;
	}
	
	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 110rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		box-sizing: content-box;
		
		.icon-group {
			display: flex;
			align-items: center;
			.icon-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				font-size: 22rpx;
				color: #606266;
				margin-right: 40rpx;
			}
		}
		
		.button-group {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			
			.action-btn {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 5rpx !important;
				height: 80rpx;
				font-size: 28rpx;
				font-weight: 600;
				border-radius: 40rpx;
				transition: all 0.3s ease;

				text {
					margin-left: 8rpx;
					font-size: 28rpx;
					font-weight: 600;
				}
			}
			
			.wechat-btn {
				background: linear-gradient(45deg, #07c160, #19be6b) !important;
				color: #fff !important;
				border: none !important;
				box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);

				&:active {
					background: linear-gradient(45deg, #06ad56, #17a85e) !important;
					transform: scale(0.98);
				}
			}

			.call-btn {
				background: linear-gradient(45deg, #fa3534, #ff6b6b) !important;
				color: #fff !important;
				border: none !important;
				box-shadow: 0 4rpx 12rpx rgba(250, 53, 52, 0.3);

				&:active {
					background: linear-gradient(45deg, #e12e2d, #ff5252) !important;
					transform: scale(0.98);
				}
			}
		}
	}
</style>
