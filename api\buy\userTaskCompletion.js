import request from '@/utils/request'

// 查询用户任务完成记录列表
export function listUserTaskCompletion(query) {
  return request({
    url: '/buy/userTaskCompletion/list',
    method: 'get',
    params: query
  })
}

// 查询用户任务完成记录详细
export function getUserTaskCompletion(completionId) {
  return request({
    url: '/buy/userTaskCompletion/' + completionId,
    method: 'get'
  })
}

// 新增用户任务完成记录
export function addUserTaskCompletion(data) {
  return request({
    url: '/buy/userTaskCompletion',
    method: 'post',
    data: data
  })
}

// 修改用户任务完成记录
export function updateUserTaskCompletion(data) {
  return request({
    url: '/buy/userTaskCompletion',
    method: 'put',
    data: data
  })
}

// 删除用户任务完成记录
export function delUserTaskCompletion(completionId) {
  return request({
    url: '/buy/userTaskCompletion/' + completionId,
    method: 'delete'
  })
}
