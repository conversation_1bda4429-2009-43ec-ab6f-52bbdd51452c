<template>
	<scroll-view 
		class="history-container" 
		scroll-y="true" 
		:refresher-enabled="true"
		:refresher-triggered="refreshing"
		@refresherrefresh="onRefresh"
		@scrolltolower="loadMore"
	>
		<!-- 浏览记录列表 -->
		<view class="history-list">
			<view 
				v-for="record in historyList" 
				:key="record.id"
				class="history-item"
				@click="goToStoreDetail(record.targetId)"
			>
				<view class="store-avatar">
					<image 
						:src="getImageUrl(record.storeAvatar)" 
						class="avatar-image" 
						mode="aspectFill"
					></image>
				</view>
				<view class="store-info">
					<text class="store-name">{{ record.storeName }}</text>
					<text class="store-business">{{ record.storeMainBusiness }}</text>
					<text class="browse-time">{{ record.createTime }}</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading && historyList.length > 0" class="loading-more">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 没有更多数据 -->
		<view v-if="!hasMore && historyList.length > 0" class="no-more">
			<text class="no-more-text">没有更多记录了</text>
		</view>

		<!-- 空状态 -->
		<view v-if="!loading && historyList.length === 0" class="empty-state">
			<text class="empty-icon">👀</text>
			<text class="empty-text">暂无浏览记录</text>
			<text class="empty-desc">浏览店铺后会在这里显示记录</text>
		</view>
	</scroll-view>
</template>

<script>
import { listSeeLog } from '@/api/buy/seeLog.js'
import config from '@/config'

export default {
	data() {
		return {
			historyList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			pageNum: 1,
			pageSize: 20,
			baseUrl: config.baseUrl
		}
	},
	onLoad() {
		this.checkLoginAndGetData()
	},
	onShow() {
		// 页面显示时刷新数据
		this.checkLoginAndGetData()
	},
	methods: {
		// 检查登录状态并获取数据
		checkLoginAndGetData() {
			// 检查用户是否登录
			if (!this.$store.state.user || !this.$store.state.user.userId) {
				uni.showModal({
					title: '提示',
					content: '请先登录后查看浏览记录',
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pages/login'
							})
						} else {
							uni.navigateBack()
						}
					}
				})
				return
			}
			this.getHistoryList()
		},

		// 获取浏览记录列表
		async getHistoryList(loadMore = false) {
			if (this.loading) return

			this.loading = true

			try {
				const params = {
					userId: this.$store.state.user.userId,
					type: '0', // 店铺浏览记录
					pageNum: loadMore ? this.pageNum : 1,
					pageSize: this.pageSize
				}

				const response = await listSeeLog(params)

				if (response.code === 200) {
					const newRecords = response.rows || []

					if (loadMore) {
						this.historyList = [...this.historyList, ...newRecords]
					} else {
						this.historyList = newRecords
						this.pageNum = 1
					}

					// 判断是否还有更多数据
					this.hasMore = newRecords.length === this.pageSize
					if (loadMore && newRecords.length > 0) {
						this.pageNum++
					}
				} else {
					uni.showToast({
						title: response.msg || '获取浏览记录失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('获取浏览记录失败:', error)
				uni.showToast({
					title: '获取浏览记录失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				this.refreshing = false
			}
		},

		// 下拉刷新
		async onRefresh() {
			this.refreshing = true
			this.pageNum = 1
			this.hasMore = true
			await this.getHistoryList()
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.getHistoryList(true)
			}
		},

		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) {
				return '/static/images/default-store.png'
			}
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath
			}
			return this.baseUrl + imagePath
		},

		// 跳转到店铺详情
		goToStoreDetail(storeId) {
			uni.navigateTo({
				url: `/pages/storeDetail/storeDetail?id=${storeId}`
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.history-container {
	height: 100vh;
	background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
	padding: 20rpx;
	box-sizing: border-box;
}

.history-list {
	.history-item {
		background: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
		display: flex;
		padding: 24rpx;
		align-items: flex-start;

		.store-avatar {
			margin-right: 20rpx;
			flex-shrink: 0;

			.avatar-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 12rpx;
			}
		}

		.store-info {
			flex: 1;
			min-width: 0;

			.store-name {
				display: block;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 12rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.store-business {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 3;
				overflow: hidden;
				font-size: 26rpx;
				color: #666;
				line-height: 1.5;
				margin-bottom: 12rpx;
			}

			.browse-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.loading-more, .no-more {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 80rpx;
	margin: 20rpx 0;

	.loading-text, .no-more-text {
		font-size: 24rpx;
		color: #999;
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;

	.empty-icon {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}

	.empty-text {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 12rpx;
	}

	.empty-desc {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
	}
}
</style>
