<template>
	<view class="page-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-back" @click="goBack">
					<uni-icons type="left" size="20" color="#fff"></uni-icons>
				</view>
				<view class="navbar-title">店铺列表</view>
			</view>
		</view>

		<!-- 下拉刷新 -->
		<scroll-view
			class="scroll-container"
			scroll-y
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore"
		>
			<!-- 行业信息及分类网格 -->
			<view class="main-content">
				<!-- 行业标题 -->
				<view class="industry-header" @click="showIndustryPicker">
					<text class="industry-name">{{ currentIndustryName || '选择行业' }}</text>
					<uni-icons type="bottom" size="14" color="#333"></uni-icons>
				</view>

				<!-- 商品分类网格 -->
				<view class="category-grid" v-if="categoryList.length > 0">
					<view
						class="category-item"
						:class="{'active': selectedCategoryId === parseInt(category.id)}"
						v-for="category in categoryList"
						:key="category.id"
						@click="selectCategory(category)"
					>
						<image
							class="category-icon"
							:src="category.iconUrl ? baseUrl + category.iconUrl : '/static/images/default-category.png'"
							mode="aspectFill"
						></image>
						<text class="category-name">{{ category.name }}</text>
					</view>
				</view>

				<!-- 店铺列表 -->
				<view class="store-list">
					<view
						class="store-card"
						v-for="store in storeList"
						:key="store.storeId"
						@click="goToStoreDetail(store)"
					>
						<image
							class="store-avatar"
							:src="getStoreAvatar(store.avatar)"
							mode="aspectFill"
						></image>
						<view class="store-info">
							<view class="store-title-line">
								<text v-if="store.levelName && store.levelId"
									  :class="'store-level-tag ' + (store.levelId === 1 ? 'store-level-diamond' : store.levelId === 2 ? 'store-level-gold' : store.levelId === 3 ? 'store-level-silver' : 'store-level-default')">
									{{ store.levelName }}
								</text>
								<text class="store-name">{{ store.storeName }}</text>
							</view>
							<view class="store-business" v-if="store.mainBusiness">{{ store.mainBusiness }}</view>
							<view class="store-contact-line">
								<view class="contact-info">
									<uni-icons type="person-filled" size="14" color="#999"></uni-icons>
									<text>{{ store.contactPerson }}</text>
								</view>
								<view class="location-info">
									<uni-icons type="location-filled" size="14" color="#999"></uni-icons>
									<text>{{ store.marketAddress || store.address || '暂无地址' }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view v-if="!loading && storeList.length === 0" class="empty-container">
					<u-empty
						mode="list"
						text="暂无店铺数据"
						icon="http://cdn.uviewui.com/uview/empty/list.png"
					></u-empty>
				</view>

				<!-- 加载更多 -->
				<u-loadmore
					v-if="storeList.length > 0"
					:status="loadmoreStatus"
					@loadmore="onLoadMore"
				></u-loadmore>
			</view>
		</scroll-view>

		<!-- 行业选择器 -->
		<u-picker
			ref="industryPicker"
			:show="showPicker"
			:columns="industryColumns"
			@confirm="onIndustryConfirm"
			@cancel="showPicker = false"
			@close="showPicker = false"
		></u-picker>
	</view>
</template>

<script>
	import { listNodes } from "@/api/buy/nodes.js"
	import { listStoreInfo, updateStoreInfo } from "@/api/buy/storeInfo.js"
	import { addSeeLog } from "@/api/buy/seeLog.js"
	import { baseUrl } from "@/config";

	export default {
		data() {
			return {
				baseUrl,
				statusBarHeight: 0,    // 状态栏高度
				marketId: null,        // 当前市场ID
				industryId: null,      // 当前选中的行业ID
				industryInfo: {},      // 当前行业的信息（名称，子分类等）
				categoryList: [],      // 商品分类列表
				selectedCategoryId: null, // 选中的分类ID
				storeList: [],         // 店铺列表
				loading: false,
				refreshing: false,     // 下拉刷新状态

				// 行业选择器相关
				showPicker: false,     // 是否显示选择器
				industryColumns: [[]],  // 行业选择器数据
				industryList: [],      // 当前市场下的所有行业
				currentIndustryName: '', // 当前选中的行业名称

				// 分页相关
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					categoryTags: null,  // 商品分类标签，用于筛选店铺
					auditStatus: 1       // 只查询审核通过的店铺
				},
				total: 0,
				loadmoreStatus: 'loadmore'
			}
		},
		onLoad(options) {
			this.setStatusBarHeight();
			if (options.id) {
				this.industryId = options.id;
				// 记录分类访问记录（用户点击的第3级分类）
				this.recordCategoryVisit(this.industryId);
				this.loadData();
			} else {
				uni.showToast({ title: '参数错误', icon: 'none' });
				uni.navigateBack();
			}
		},
		methods: {
			// 设置状态栏高度
			setStatusBarHeight() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 显示行业选择器
			showIndustryPicker() {
				if (this.industryList.length > 0) {
					this.showPicker = true;
				}
			},

			// 行业选择确认
			onIndustryConfirm(e) {
				console.log('选择器确认事件:', e);
				console.log('选择器索引:', e.indexs);
				console.log('选择器值:', e.value);
				console.log('当前行业列表:', this.industryList);

				// 使用索引来获取选中的行业
				const selectedIndex = e.indexs[0];
				const selectedIndustry = this.industryList[selectedIndex];
				console.log('选中的索引:', selectedIndex);
				console.log('选中的行业:', selectedIndustry);

				if (selectedIndustry) {
					this.industryId = selectedIndustry.id;
					this.currentIndustryName = selectedIndustry.name;

					// 重置分页参数
					this.queryParams.pageNum = 1;
					this.storeList = [];
					this.total = 0;
					this.loadmoreStatus = 'loadmore';

					console.log('更新后的行业ID:', this.industryId);
					console.log('更新后的行业名称:', this.currentIndustryName);

					this.loadCategoriesAndStores();
				} else {
					console.error('未找到选中的行业，索引:', selectedIndex, '行业列表长度:', this.industryList.length);
				}
				this.showPicker = false;
			},

			async loadData() {
				this.loading = true;
				try {
					const res = await listNodes({pageSize:1000000});
					const flatList = res.rows || [];
					const nodeMap = new Map(flatList.map(node => [parseInt(node.id), node]));

					// 1. 找到当前传入的节点信息
					const currentNode = nodeMap.get(parseInt(this.industryId));
					if (!currentNode) {
						uni.showToast({ title: '未找到指定节点信息', icon: 'none' });
						this.loading = false;
						return;
					}

					// 2. 根据传入的节点类型，确定市场和行业列表
					let market = null;

					if (currentNode.nodeType === 'category') {
						const parentNode = nodeMap.get(parseInt(currentNode.parentId));
						if (parentNode && parentNode.nodeType === 'market') {
							// 传入的是第三级分类（行业），父节点是市场
							market = parentNode;
							this.marketId = market.id;
							this.industryId = currentNode.id;
							this.currentIndustryName = currentNode.name;
						} else if (parentNode && parentNode.nodeType === 'category') {
							// 传入的是第四级分类，需要找到市场
							const thirdCategory = parentNode;
							market = nodeMap.get(parseInt(thirdCategory.parentId));
							this.marketId = market.id;
							this.industryId = thirdCategory.id;
							this.currentIndustryName = thirdCategory.name;
						}
					} else if (currentNode.nodeType === 'market') {
						// 传入的是市场，需要选择第一个行业
						market = currentNode;
						this.marketId = market.id;
						const industries = flatList.filter(node =>
							parseInt(node.parentId) === parseInt(market.id) && node.nodeType === 'category'
						);
						if (industries.length > 0) {
							this.industryId = industries[0].id;
							this.currentIndustryName = industries[0].name;
						}
					}

					if (!market) {
						uni.showToast({ title: '无法确定市场信息', icon: 'none' });
						this.loading = false;
						return;
					}

					// 3. 获取当前市场下的所有行业（第三级分类）
					this.industryList = flatList.filter(node =>
						parseInt(node.parentId) === parseInt(this.marketId) && node.nodeType === 'category'
					);

					// 4. 构建行业选择器数据
					this.industryColumns = [this.industryList.map(industry => industry.name)];

					// 5. 加载当前行业下的分类和店铺
					await this.loadCategoriesAndStores();

				} catch (error) {
					console.error("Data loading failed:", error);
					uni.showToast({ title: '数据加载失败', icon: 'none' });
					this.loading = false;
				}
			},

			// 加载分类和店铺数据
			async loadCategoriesAndStores() {
				console.log('开始加载分类和店铺数据，当前行业ID:', this.industryId);
				this.loading = true;

				try {
					const res = await listNodes({pageSize:1000000});
					const flatList = res.rows || [];
					console.log('获取到的节点数据:', flatList.length);

					// 获取当前行业下的第四级分类
					this.categoryList = flatList.filter(node => {
						if (node.nodeType !== 'category') return false;
						const parent = flatList.find(p => parseInt(p.id) === parseInt(node.parentId));
						const isMatch = parent && parseInt(parent.id) === parseInt(this.industryId);
						if (isMatch) {
							console.log('找到匹配的第四级分类:', node.name, 'parentId:', node.parentId);
						}
						return isMatch;
					});

					console.log('筛选后的分类列表:', this.categoryList);

					// 如果有分类，默认选择第一个分类
					if (this.categoryList.length > 0) {
						this.selectedCategoryId = parseInt(this.categoryList[0].id);
						this.queryParams.categoryTags = this.selectedCategoryId;
						console.log('选择第一个分类ID:', this.selectedCategoryId);
						await this.loadStoreList();
					} else {
						// 如果没有第四级分类，直接用第三级分类ID查询
						this.queryParams.categoryTags = this.industryId;
						console.log('没有第四级分类，使用行业ID查询:', this.industryId);
						await this.loadStoreList();
					}

					this.loading = false;
				} catch (error) {
					console.error("Categories loading failed:", error);
					this.loading = false;
				}
			},

			// 加载店铺列表
			async loadStoreList(isLoadMore = false) {
				if (this.loading) {
					return;
				}

				this.loading = true;
				if (!isLoadMore) {
					this.loadmoreStatus = 'loading';
				}

				try {
					const res = await listStoreInfo(this.queryParams);
					this.loading = false;

					if (res.code === 200) {
						const newList = res.rows || [];

						if (isLoadMore) {
							this.storeList = [...this.storeList, ...newList];
						} else {
							this.storeList = newList;
						}

						this.total = res.total || 0;

						// 更新加载更多状态
						if (this.storeList.length >= this.total) {
							this.loadmoreStatus = 'nomore';
						} else {
							this.loadmoreStatus = 'loadmore';
						}
					} else {
						uni.showToast({ title: res.msg || '获取店铺列表失败', icon: 'none' });
						this.loadmoreStatus = 'loadmore';
					}
				} catch (error) {
					this.loading = false;
					this.loadmoreStatus = 'loadmore';
					console.error('获取店铺列表失败:', error);
					uni.showToast({ title: '获取店铺列表失败', icon: 'none' });
				}
			},

			// 选择分类
			async selectCategory(category) {
				const categoryId = parseInt(category.id);
				if (this.selectedCategoryId === categoryId) return;

				this.selectedCategoryId = categoryId;
				this.queryParams.categoryTags = categoryId;
				this.queryParams.pageNum = 1;

				// 重置店铺列表
				this.storeList = [];
				this.total = 0;
				this.loadmoreStatus = 'loadmore';

				console.log('切换分类，新的查询参数:', this.queryParams);

				// 记录分类访问记录
				this.recordCategoryVisit(categoryId);

				await this.loadStoreList();
			},

			// 下拉刷新
			async onRefresh() {
				this.refreshing = true;
				this.queryParams.pageNum = 1;

				// 重置店铺列表
				this.storeList = [];
				this.total = 0;
				this.loadmoreStatus = 'loadmore';

				await this.loadStoreList();
				this.refreshing = false;
			},

			// 加载更多
			async onLoadMore() {
				if (this.loadmoreStatus === 'loadmore') {
					this.queryParams.pageNum++;
					await this.loadStoreList(true);
				}
			},

			// 获取店铺头像
			getStoreAvatar(avatar) {
				if (!avatar) return '/static/images/default-avatar.png';

				// 如果不是完整URL，拼接baseUrl
				if (avatar && !avatar.startsWith('http://') && !avatar.startsWith('https://')) {
					return this.baseUrl + avatar;
				}
				return avatar;
			},

			// 跳转到店铺详情
			async goToStoreDetail(store) {
				try {
					// 更新店铺浏览量
					await this.updateStoreViews(store.storeId);

					// 跳转到详情页
					uni.navigateTo({
						url: `/pages/storeDetail/storeDetail?id=${store.storeId}`
					});
				} catch (error) {
					console.error('跳转店铺详情失败:', error);
					// 即使更新浏览量失败，也要跳转到详情页
					uni.navigateTo({
						url: `/pages/storeDetail/storeDetail?id=${store.storeId}`
					});
				}
			},

			// 更新店铺浏览量
			async updateStoreViews(storeId) {
				try {
					// 这里需要根据实际的API来调用，可能需要先获取店铺信息再更新
					// 假设有一个增加浏览量的字段，比如 visitorCount
					const storeInfo = this.storeList.find(store => store.storeId === storeId);
					if (storeInfo) {
						const updateData = {
							...storeInfo,
							visitorCount: (storeInfo.visitorCount || 0) + 1
						};
						await updateStoreInfo(updateData);
					}
				} catch (error) {
					console.error('更新店铺浏览量失败:', error);
				}
			},

			// 记录分类访问记录
			recordCategoryVisit(categoryId) {
				try {
					// 获取用户ID，如果未登录则为0
					let userId = 0;
					if (this.$store.state.user && this.$store.state.user.userId) {
						userId = this.$store.state.user.userId;
					}

					const visitData = {
						userId: userId,
						targetId: categoryId,
						type: '1' // 分类访问记录
					};

					// 异步记录访问，不影响页面加载
					addSeeLog(visitData).then(res => {
						console.log('分类访问记录已记录，targetId:', categoryId, 'response:', res);
					}).catch(error => {
						console.error('记录分类访问失败:', error);
					});
				} catch (error) {
					console.error('记录访问记录异常:', error);
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		background: linear-gradient(to bottom, #FF8A00, #ffffff 50%);
	}

	/* 自定义导航栏样式 */
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: transparent;

		.navbar-content {
			height: 88rpx;
			display: flex;
			align-items: center;
			position: relative;
			padding: 0 30rpx;

			.navbar-back {
				position: absolute;
				left: 30rpx;
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.navbar-title {
				flex: 1;
				font-size: 36rpx;
				font-weight: 600;
				color: #fff;
				text-align: center;
				text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
			}
		}
	}

	.page-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
	}

	.scroll-container {
		flex: 1;
		height: 100%;
	}

	.main-content {
		padding: 20rpx;
	}

	.industry-header {
		display: flex;
		align-items: center;
		padding: 10rpx 0;
		margin-bottom: 20rpx;
		.industry-name {
			font-size: 32rpx;
			font-weight: bold;
			margin-right: 10rpx;
		}
	}

	.category-grid {
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 20rpx;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		.category-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			transition: all 0.3s;
			cursor: pointer;

			&.active {
				background: #e3f2fd;
				border: 2px solid #2196f3;
				transform: scale(1.05);
			}

			.category-icon {
				width: 90rpx;
				height: 90rpx;
				border-radius: 50%;
				background-color: #f0f0f0;
				margin-bottom: 10rpx;
			}
			.category-name {
				font-size: 24rpx;
				color: #666;
				text-align: center;
			}
		}
	}

	.store-list {
		.store-card {
			display: flex;
			background-color: #fff;
			padding: 20rpx;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			transition: all 0.3s;

			&:active {
				transform: scale(0.98);
				background: #f8f9fa;
			}

			.store-avatar {
				width: 200rpx;
				height: 200rpx;
				border-radius: 8rpx;
				margin-right: 20rpx;
				flex-shrink: 0;
			}
			.store-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				.store-title-line {
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;

					/* 店铺等级标签基础样式 */
					.store-level-tag {
						font-size: 20rpx;
						padding: 4rpx 12rpx;
						border-radius: 6rpx;
						margin-right: 10rpx;
						color: #fff;
						font-weight: bold;
					}

					/* 店铺等级标签样式 */
					.store-level-diamond {
						background: linear-gradient(45deg, #FFD700, #FFA500);
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 12rpx;
						border-radius: 6rpx;
						margin-right: 10rpx;
						font-weight: bold;
						box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
					}

					.store-level-gold {
						background: linear-gradient(45deg, #C0C0C0, #A8A8A8);
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 12rpx;
						border-radius: 6rpx;
						margin-right: 10rpx;
						font-weight: bold;
						box-shadow: 0 2rpx 8rpx rgba(192, 192, 192, 0.3);
					}

					.store-level-silver {
						background: linear-gradient(45deg, #CD7F32, #B8860B);
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 12rpx;
						border-radius: 6rpx;
						margin-right: 10rpx;
						font-weight: bold;
						box-shadow: 0 2rpx 8rpx rgba(205, 127, 50, 0.3);
					}

					.store-level-default {
						background: linear-gradient(45deg, #999, #777);
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 12rpx;
						border-radius: 6rpx;
						margin-right: 10rpx;
					}
				}

				.store-name {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				.store-business {
					font-size: 24rpx;
					color: #666;
					line-height: 1.5;
					margin-bottom: 10rpx;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
				}
				.store-contact-line {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 24rpx;
					color: #999;
					margin-top: auto;
					.contact-info, .location-info {
						display: flex;
						align-items: center;
						text {
							margin-left: 5rpx;
						}
					}
					.location-info {
						max-width: 240rpx;
						text {
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}
				}
			}
		}
	}

	.empty-container {
		padding-top: 200rpx;
	}
</style>