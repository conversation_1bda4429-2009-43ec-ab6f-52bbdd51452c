<template>
	<view class="friend-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title">商友</view>
			</view>
		</view>

		<!-- 顶部搜索栏 -->
		<view class="search-container">
			<view class="search-bar">
				<uni-icons type="search" color="#999" size="18"></uni-icons>
				<input
					class="search-input"
					placeholder="搜索商友"
					v-model="searchKeyword"
					@input="onSearchInput"
				/>
			</view>
		</view>

		<!-- 商友列表 -->
		<view class="content-container">
			<scroll-view class="friend-scroll" scroll-y="true" @scrolltolower="loadMoreFriends">
				<view class="friend-list">
					<!-- 加载状态 -->
					<view v-if="loading" class="loading-state">
						<text>正在加载...</text>
					</view>
					
					<!-- 商友列表 -->
					<view v-else-if="displayFriendList.length > 0">
						<view
							v-for="(friend, index) in displayFriendList"
							:key="index"
							class="friend-item"
							@click="goToStoreDetail(friend)"
						>
							<view class="store-friend">
								<image
									:src="getImageUrl(friend.storeInfo.storeAvatar)"
									class="store-avatar"
									mode="aspectFill"
								></image>
								<view class="store-info">
									<text class="store-name">{{ friend.storeInfo.storeName }}</text>
									<text class="store-business">{{ getBusinessPreview(friend.storeInfo.mainBusiness) }}</text>
									<view class="store-bottom-info">
										<view class="info-item">
											<text class="icon">📍</text>
											<text class="info-text">{{ friend.storeInfo.marketAddress }}</text>
										</view>
										<view class="info-item">
											<text class="icon">👤</text>
											<text class="info-text">{{ friend.storeInfo.contactPerson }}</text>
										</view>
									</view>
								</view>
								<view class="action-btn">
									<uni-icons type="right" color="#ccc" size="16"></uni-icons>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 空状态 -->
					<view v-else class="empty-friends">
						<uni-icons type="staff" color="#ccc" size="80"></uni-icons>
						<text class="empty-text">暂无商友</text>
						<text class="empty-desc">收藏商家后会显示在这里</text>
					</view>
					
					<!-- 加载更多 -->
					<view v-if="hasMore && displayFriendList.length > 0" class="load-more">
						<text>加载更多...</text>
					</view>
					<view v-else-if="displayFriendList.length > 0" class="no-more">
						<text>没有更多了</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar ref="customTabbar" :current="1" @change="onTabChange"></custom-tabbar>
	</view>
</template>

<script>
import { listFavorite } from '@/api/buy/favorite'
import config from '@/config'
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

export default {
	components: {
		CustomTabbar
	},
	data() {
		return {
			friendList: [],
			displayFriendList: [],
			searchKeyword: '',
			pageNum: 1,
			pageSize: 20,
			hasMore: true,
			loading: false,
			baseUrl: config.baseUrl,
			statusBarHeight: 0 // 状态栏高度
		}
	},

	onLoad() {
		this.setStatusBarHeight();
		this.getFriendList();
	},

	onShow() {
		// 同步tabBar状态
		this.$nextTick(() => {
			if (this.$refs.customTabbar) {
				this.$refs.customTabbar.syncCurrentPageState();
			}
		});
	},

	methods: {
		// 设置状态栏高度
		setStatusBarHeight() {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 0;
		},

		// 搜索输入处理
		onSearchInput() {
			this.filterFriendList();
		},

		// 过滤商友列表
		filterFriendList() {
			if (!this.searchKeyword.trim()) {
				this.displayFriendList = [...this.friendList];
				return;
			}
			
			this.displayFriendList = this.friendList.filter(friend => {
				const storeName = friend.storeInfo?.storeName || '';
				const contactPerson = friend.storeInfo?.contactPerson || '';
				const mainBusiness = friend.storeInfo?.mainBusiness || '';
				const keyword = this.searchKeyword.toLowerCase();
				
				return storeName.toLowerCase().includes(keyword) ||
					   contactPerson.toLowerCase().includes(keyword) ||
					   mainBusiness.toLowerCase().includes(keyword);
			});
		},

		// 获取商友列表（只获取商家类型的收藏）
		async getFriendList(loadMore = false) {
			if (this.loading) return;
			
			if (!this.$store.state.user || !this.$store.state.user.userId) {
				this.friendList = [];
				this.displayFriendList = [];
				return;
			}
			
			this.loading = true;
			
			try {
				const params = {
					userId: this.$store.state.user.userId,
					pageNum: loadMore ? this.pageNum : 1,
					pageSize: this.pageSize,
					targetType: 'store'
				};
				
				const response = await listFavorite(params);
				if (response.code === 200) {
					const newFriends = response.rows || [];
					
					if (loadMore) {
						this.friendList = [...this.friendList, ...newFriends];
					} else {
						this.friendList = newFriends;
						this.pageNum = 1;
					}
					
					this.filterFriendList();
					
					this.hasMore = newFriends.length === this.pageSize;
					if (loadMore && newFriends.length > 0) {
						this.pageNum++;
					}
				} else {
					this.friendList = [];
					this.displayFriendList = [];
				}
			} catch (error) {
				console.error('获取商友列表失败:', error);
				this.friendList = [];
				this.displayFriendList = [];
			} finally {
				this.loading = false;
			}
		},
		
		// 加载更多商友
		loadMoreFriends() {
			if (this.hasMore && !this.loading) {
				this.getFriendList(true);
			}
		},
		
		// 跳转到商家详情页
		goToStoreDetail(friend) {
			uni.navigateTo({
				url: `/pages/storeDetail/storeDetail?id=${friend.targetId}`
			});
		},
		
		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '/static/images/profile.jpg';
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			return this.baseUrl + imagePath;
		},
		
		// 获取主营业务预览
		getBusinessPreview(business) {
			if (!business) return '';
			return business.length > 30 ? business.substring(0, 30) + '...' : business;
		},

		// tabBar切换事件
		onTabChange(e) {
			console.log('Tab切换到:', e.index, e.pagePath);
		}
	}
}
</script>

<style lang="scss">
page {
	height: 100%;
	background: linear-gradient(to bottom, #FF8A00, #ffffff 50%);
}

/* 自定义导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: transparent;

	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;

		.navbar-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #fff;
			text-align: center;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
		}
	}
}

.friend-container {
	min-height: 100vh;
	background-color: transparent;
	padding-bottom: 120rpx; /* 为自定义tabBar预留空间 */
}

.search-container {
	background-color: transparent;
	padding: 20rpx 30rpx;

	.search-bar {
		display: flex;
		align-items: center;
		background-color: rgba(255, 255, 255, 0.9);
		border-radius: 25rpx;
		padding: 0 24rpx;
		height: 70rpx;
		backdrop-filter: blur(10px);

		.search-input {
			flex: 1;
			margin-left: 16rpx;
			font-size: 28rpx;
			color: #333;
		}
	}
}

.content-container {
	flex: 1;
	
	.friend-scroll {
		height: calc(100vh - 110rpx);
		
		.friend-list {
			padding: 20rpx;
			
			.friend-item {
				background-color: #fff;
				border-radius: 12rpx;
				margin-bottom: 20rpx;
				overflow: hidden;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

				.store-friend {
					display: flex;
					padding: 24rpx;
					align-items: flex-start;

					.store-avatar {
						width: 120rpx;
						height: 120rpx;
						border-radius: 12rpx;
						margin-right: 20rpx;
						flex-shrink: 0;
					}

					.store-info {
						flex: 1;

						.store-name {
							font-size: 32rpx;
							font-weight: bold;
							color: #DD1A21;
							margin-bottom: 12rpx;
							display: block;
						}

						.store-business {
							font-size: 26rpx;
							color: #666;
							line-height: 1.4;
							margin-bottom: 12rpx;
							display: block;
						}

						.store-bottom-info {
							display: flex;
							flex-direction: column;
							gap: 8rpx;

							.info-item {
								display: flex;
								align-items: center;

								.icon {
									font-size: 24rpx;
									margin-right: 8rpx;
									width: 24rpx;
									text-align: center;
								}

								.info-text {
									font-size: 24rpx;
									color: #999;
									flex: 1;
								}
							}
						}
					}

					.action-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 40rpx;
						height: 40rpx;
						margin-left: 10rpx;
					}
				}
			}
			
			.loading-state, .load-more, .no-more {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;
				
				text {
					font-size: 24rpx;
					color: #999;
				}
			}

			.empty-friends {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 400rpx;
				
				.empty-text {
					font-size: 28rpx;
					color: #666;
					margin-top: 20rpx;
					margin-bottom: 8rpx;
				}

				.empty-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
}
</style>
