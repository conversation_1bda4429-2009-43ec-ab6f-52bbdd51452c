<template>
	<custom-tabbar :current="currentIndex" @change="onTabChange"></custom-tabbar>
</template>

<script>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

export default {
	name: 'CustomTabBar',
	components: {
		CustomTabbar
	},
	data() {
		return {
			currentIndex: 2
		}
	},
	methods: {
		onTabChange(e) {
			this.currentIndex = e.index;
		},
		
		// 更新当前选中的tab
		updateCurrentIndex(index) {
			this.currentIndex = index;
		}
	}
}
</script>

<style>
</style>
