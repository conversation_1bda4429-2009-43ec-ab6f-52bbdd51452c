<template>
	<view class="search-result-container">
		<!-- 搜索栏 -->
		<view class="search-header">
			<view class="search-bar">
				<view class="search-input-wrapper">
					<uni-icons type="search" size="18" color="#999"></uni-icons>
					<input 
						class="search-input" 
						type="text" 
						v-model="keyword" 
						placeholder="请输入关键词" 
						@confirm="handleSearch"
					/>
				</view>
				<button class="search-btn" @click="handleSearch">搜索</button>
			</view>
		</view>

		<!-- 搜索信息 -->
		<view class="search-info">
			<text class="search-text">
				<template v-if="searchType === 'keyword'">
					搜索"{{ originalKeyword }}"的结果
				</template>
				<template v-else>
					{{ categoryName }}分类的结果
				</template>
			</text>
		</view>

		<!-- 标签页 -->
		<view class="tabs-container">
			<view class="tabs">
				<view
					v-for="(tab, index) in tabs"
					:key="index"
					class="tab-item"
					:class="{ active: activeTabIndex === index }"
					@click="switchTab(index)"
				>
					<view class="tab-content">
						<text class="tab-emoji">{{ tab.emoji }}</text>
						<text class="tab-text">{{ tab.name }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 搜索结果列表 -->
		<scroll-view
			class="result-content"
			scroll-y="true"
			@scrolltolower="loadMore"
			:refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>
			<view v-if="activeTabIndex === 0" class="store-results">
				<!-- 商家搜索结果 -->
				<view v-if="storeResults.length > 0">
					<view
						v-for="store in storeResults"
						:key="store.storeId"
						class="store-item"
						@click="goToStoreDetail(store.storeId)"
					>
						<image
							:src="getImageUrl(store.avatar)"
							class="store-avatar"
							mode="aspectFill"
						></image>
						<view class="store-info">
							<view class="store-title-line">
								<text v-if="store.levelName && store.levelId"
									  :class="'store-level-tag ' + (store.levelId === 1 ? 'store-level-diamond' : store.levelId === 2 ? 'store-level-gold' : store.levelId === 3 ? 'store-level-silver' : 'store-level-default')">
									{{ store.levelName }}
								</text>
								<text class="store-name">{{ store.storeName }}</text>
							</view>
							<text class="store-business">{{ store.mainBusiness }}</text>
							<view class="store-contact">
								<text class="contact-person">👤 {{ store.contactPerson }}</text>
								<text class="market-address">📍 {{ store.marketAddress }}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-else-if="!loading && searchExecuted" class="empty-state">
					<text class="empty-emoji">🏪</text>
					<text class="empty-text">暂无相关商家</text>
				</view>
			</view>

			<view v-else class="product-results">
				<!-- 产品搜索结果 -->
				<view v-if="productResults.length > 0" class="product-grid">
					<view
						v-for="product in productResults"
						:key="product.productId"
						class="product-card"
					>
						<view class="product-image-container" @click="goToProductDetail(product.productId)">
							<image
								:src="getProductImage(product.productImages)"
								class="product-image"
								mode="aspectFill"
							></image>
						</view>
						<view class="product-info">
							<text class="product-name" @click="goToProductDetail(product.productId)">{{ product.productName }}</text>
							<view class="product-actions">
								<view class="store-tag" @click="goToStoreDetail(product.storeId)">
									<text class="store-name">{{ product.storeName }}</text>
								</view>
								<view class="visit-store-btn" @click="goToStoreDetail(product.storeId)">
									<text class="visit-text">进店逛逛</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-else-if="!loading && searchExecuted" class="empty-state">
					<text class="empty-emoji">📦</text>
					<text class="empty-text">暂无相关产品</text>
				</view>
			</view>

			<!-- 加载更多 -->
			<view v-if="loading" class="loading-more">
				<uni-icons type="spinner-cycle" size="30" color="#999"></uni-icons>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMore && searchExecuted && (storeResults.length > 0 || productResults.length > 0)" class="no-more">
				<text class="no-more-text">没有更多数据了</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { search } from '@/api/buy/storeInfo'
	import config from '@/config'

	export default {
		data() {
			return {
				keyword: '',
				originalKeyword: '', // 原始搜索关键词
				searchType: 'keyword', // keyword 或 category
				categoryId: '',
				categoryName: '',
				cityInfo: null,
				activeTabIndex: 0,
				tabs: [
					{ name: '商家', emoji: '🏪' },
					{ name: '产品', emoji: '📦' }
				],
				storeResults: [],
				productResults: [],
				// 分页参数
				pageNum: 1,
				pageSize: 10,
				hasMore: true,
				loading: false,
				refreshing: false,
				searchExecuted: false, // 是否已执行过搜索
				baseUrl: config.baseUrl
			}
		},
		
		onLoad(options) {
			// 获取搜索参数
			if (options.keyword) {
				this.keyword = decodeURIComponent(options.keyword);
				this.originalKeyword = this.keyword;
				this.searchType = options.type || 'keyword';
			}

			if (options.categoryId) {
				this.categoryId = options.categoryId;
				this.categoryName = decodeURIComponent(options.categoryName || '');
				this.searchType = 'category';
				// 如果是分类搜索，使用分类名作为关键词
				if (!this.keyword) {
					this.keyword = this.categoryName;
					this.originalKeyword = this.categoryName;
				}
			}

			if (options.cityInfo) {
				this.cityInfo = JSON.parse(decodeURIComponent(options.cityInfo));
			}

			// 执行搜索
			this.performSearch();
		},
		
		methods: {
			// 执行搜索
			async performSearch(isLoadMore = false) {
				if (this.loading) return;

				// 如果不是加载更多，重置分页参数
				if (!isLoadMore) {
					this.pageNum = 1;
					this.hasMore = true;
					this.storeResults = [];
					this.productResults = [];
				}

				this.loading = true;
				this.searchExecuted = true;

				try {
					const searchParams = {
						keyword: this.keyword || '',
						city: this.cityInfo?.name || '',
						searchType: this.activeTabIndex === 0 ? 'store' : 'product',
						pageNum: this.pageNum,
						pageSize: this.pageSize
					};

					const res = await search(searchParams);

					if (res.code === 200) {
						const newResults = res.rows || [];

						if (this.activeTabIndex === 0) {
							// 商家搜索结果
							if (isLoadMore) {
								this.storeResults = [...this.storeResults, ...newResults];
							} else {
								this.storeResults = newResults;
							}
						} else {
							// 产品搜索结果
							if (isLoadMore) {
								this.productResults = [...this.productResults, ...newResults];
							} else {
								this.productResults = newResults;
							}
						}

						// 判断是否还有更多数据
						this.hasMore = newResults.length >= this.pageSize;

						if (this.hasMore) {
							this.pageNum++;
						}
					} else {
						uni.showToast({
							title: res.msg || '搜索失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('搜索失败:', error);
					uni.showToast({
						title: '搜索失败，请重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					this.refreshing = false;
				}
			},
			
			// 处理搜索
			handleSearch() {
				if (!this.keyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键词',
						icon: 'none'
					});
					return;
				}
				
				// 更新搜索参数
				this.originalKeyword = this.keyword;
				this.searchType = 'keyword';
				this.categoryId = '';
				this.categoryName = '';
				
				// 重新执行搜索
				this.performSearch();
			},
			
			// 切换标签页
			switchTab(index) {
				if (this.activeTabIndex === index) return;

				this.activeTabIndex = index;
				// 切换tab时重新搜索
				this.performSearch();
			},

			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.performSearch(true);
				}
			},

			// 下拉刷新
			onRefresh() {
				this.refreshing = true;
				this.performSearch();
			},

			// 获取图片URL
			getImageUrl(imageUrl) {
				if (!imageUrl) {
					return '/static/images/default-store.png';
				}
				if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
					return imageUrl;
				}
				return this.baseUrl + imageUrl;
			},

			// 获取产品图片（取第一张）
			getProductImage(productImages) {
				if (!productImages) {
					return '/static/images/default-product.png';
				}
				const images = productImages.split(',');
				const firstImage = images[0].trim();

				if (firstImage.startsWith('http://') || firstImage.startsWith('https://')) {
					return firstImage;
				}
				return this.baseUrl + firstImage;
			},

			// 跳转到店铺详情
			goToStoreDetail(storeId) {
				uni.navigateTo({
					url: `/pages/storeDetail/storeDetail?id=${storeId}`
				});
			},

			// 跳转到产品详情
			goToProductDetail(productId) {
				uni.navigateTo({
					url: `/pages/productDetail/productDetail?id=${productId}`
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.search-result-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.search-header {
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-bottom: 1px solid #eee;
		
		.search-bar {
			display: flex;
			align-items: center;
			background-color: #f8f8f8;
			border: 1px solid #DD1A21;
			border-radius: 40rpx;
			padding: 0 10rpx;
			height: 80rpx;

			.search-input-wrapper {
				flex: 1;
				display: flex;
				align-items: center;
				padding: 0 20rpx;
				
				.search-input {
					width: 100%;
					font-size: 28rpx;
					margin-left: 10rpx;
				}
			}

			.search-btn {
				background-color: #DD1A21;
				color: #fff;
				height: 64rpx;
				line-height: 64rpx;
				font-size: 28rpx;
				border-radius: 32rpx;
				padding: 0 30rpx;
				border: none;
			}
		}
	}

	.search-info {
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-bottom: 1px solid #eee;
		
		.search-text {
			font-size: 28rpx;
			color: #666;
		}
	}

	.tabs-container {
		background-color: #fff;
		border-bottom: 1px solid #eee;

		.tabs {
			display: flex;

			.tab-item {
				flex: 1;
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 30rpx 0;
				position: relative;

				&.active {
					.tab-content {
						.tab-text {
							color: #DD1A21;
						}
					}

					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 60rpx;
						height: 4rpx;
						background-color: #DD1A21;
						border-radius: 2rpx;
					}
				}

				.tab-content {
					display: flex;
					align-items: center;

					.tab-emoji {
						font-size: 32rpx;
						margin-right: 8rpx;
					}

					.tab-text {
						font-size: 28rpx;
						color: #666;
					}
				}
			}
		}
	}

	.result-content {
		flex: 1;
		height: calc(100vh - 300rpx);

		.store-results,
		.product-results {
			padding: 20rpx;
		}

		.store-item {
			display: flex;
			background-color: #fff;
			border-radius: 10rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);

			.store-avatar {
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
				margin-right: 20rpx;
			}

			.store-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.store-title-line {
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;

					/* 店铺等级标签基础样式 */
					.store-level-tag {
						font-size: 18rpx;
						padding: 3rpx 10rpx;
						border-radius: 4rpx;
						margin-right: 8rpx;
						color: #fff;
						font-weight: bold;
					}

					/* 店铺等级标签样式 */
					.store-level-diamond {
						background: linear-gradient(45deg, #FFD700, #FFA500);
						color: #fff;
						font-size: 18rpx;
						padding: 3rpx 10rpx;
						border-radius: 4rpx;
						margin-right: 8rpx;
						font-weight: bold;
						box-shadow: 0 2rpx 6rpx rgba(255, 215, 0, 0.3);
					}

					.store-level-gold {
						background: linear-gradient(45deg, #C0C0C0, #A8A8A8);
						color: #fff;
						font-size: 18rpx;
						padding: 3rpx 10rpx;
						border-radius: 4rpx;
						margin-right: 8rpx;
						font-weight: bold;
						box-shadow: 0 2rpx 6rpx rgba(192, 192, 192, 0.3);
					}

					.store-level-silver {
						background: linear-gradient(45deg, #CD7F32, #B8860B);
						color: #fff;
						font-size: 18rpx;
						padding: 3rpx 10rpx;
						border-radius: 4rpx;
						margin-right: 8rpx;
						font-weight: bold;
						box-shadow: 0 2rpx 6rpx rgba(205, 127, 50, 0.3);
					}

					.store-level-default {
						background: linear-gradient(45deg, #999, #777);
						color: #fff;
						font-size: 18rpx;
						padding: 3rpx 10rpx;
						border-radius: 4rpx;
						margin-right: 8rpx;
					}
				}

				.store-name {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}

				.store-business {
					font-size: 26rpx;
					color: #666;
					margin-bottom: 15rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 3;
					line-clamp: 3;
					-webkit-box-orient: vertical;
					line-height: 1.4;
					max-height: calc(1.4em * 3);
				}

				.store-contact {
					display: flex;
					flex-direction: column;
					gap: 8rpx;

					.contact-person,
					.market-address {
						font-size: 24rpx;
						color: #999;
					}
				}
			}
		}

		.product-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20rpx;
			padding: 0 10rpx;
		}

		.product-card {
			background-color: #fff;
			border-radius: 10rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);

			.product-image-container {
				position: relative;
				width: 100%;
				height: 300rpx;

				.product-image {
					width: 100%;
					height: 100%;
				}
			}

			.product-info {
				padding: 20rpx;

				.product-name {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 15rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.product-actions {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.store-tag {
						flex: 1;
						margin-right: 10rpx;

						.store-name {
							font-size: 24rpx;
							color: #666;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}

					.visit-store-btn {
						background-color: #DD1A21;
						color: #fff;
						padding: 8rpx 16rpx;
						border-radius: 20rpx;
						flex-shrink: 0;

						.visit-text {
							font-size: 22rpx;
							color: #fff;
						}
					}
				}
			}
		}

		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 100rpx 0;

			.empty-emoji {
				font-size: 80rpx;
				margin-bottom: 20rpx;
			}

			.empty-text {
				font-size: 28rpx;
				color: #999;
			}
		}

		.loading-more {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 30rpx 0;

			.loading-text {
				font-size: 26rpx;
				color: #999;
				margin-left: 10rpx;
			}
		}

		.no-more {
			display: flex;
			justify-content: center;
			padding: 30rpx 0;

			.no-more-text {
				font-size: 26rpx;
				color: #ccc;
			}
		}
	}
</style>
