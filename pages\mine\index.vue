<template>
	<view class="mine-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title">我的</view>
			</view>
		</view>

		<!-- 用户信息区域 -->
		<view class="profile-section">
			<image @click="handleAvatarClick" :src="avatar || '/static/images/profile.jpg'" class="avatar" mode="aspectFill"></image>
			<view class="user-details">
				<text v-if="isLoggedIn" class="user-name">HI {{ name || '微信用户' }}</text>
				<text v-else class="login-btn" @click="handleLogin">立即登录</text>
				<text v-if="isLoggedIn" class="edit-btn" @click="handleToEditInfo">编辑</text>
			</view>
		</view>

		<!-- 我的店铺卡片 -->
		<view class="card store-card">
			<view class="info-content">
				<view class="title-line">
					<uni-icons type="shop-filled" size="22" color="#DD1A21"></uni-icons>
					<text class="title">我的店铺</text>
				</view>
				<text class="subtitle">{{ storeDisplayText }}</text>
				<button class="open-btn" @click="handleStoreAction">{{ storeButtonText }}</button>
			</view>
			<!-- 重新添加的3D店铺图片 -->
			<image class="store-3d-image" src="/static/images/store-3d.png" mode="widthFix"></image>
		</view>

		<!-- 菜单列表 -->
		<view class="card menu-list">
			<view class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="handleMenuClick(item)">
				<view class="menu-item-left">
					<uni-icons :type="item.icon" size="22" color="#333"></uni-icons>
					<text class="menu-label">{{ item.label }}</text>
				</view>
				<uni-icons type="right" size="16" color="#C8C7CC"></uni-icons>
			</view>
		</view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar ref="customTabbar" :current="4" @change="onTabChange"></custom-tabbar>
	</view>
</template>

<script>
	import { listStoreInfo } from "@/api/buy/storeInfo.js"
	import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				statusBarHeight: 0, // 状态栏高度
				// 菜单项数据
				menuItems: [
					{ label: '浏览记录', icon: 'eye-filled', path: '/pages/mine/history' },
					{ label: '我的收藏', icon: 'heart-filled', path: '/pages/mine/favorites' },
					{ label: '我的名片', icon: 'contact-filled', path: '/pages/mine/card/index' },
					{ label: '我的积分', icon: 'medal-filled', path: '/pages/mine/points' },
					{ label: '联系客服', icon: 'headphones', action: 'contact' },
					{ label: '退出登录', icon: 'tune-filled', action: 'logout' }
				],
				storeInfo: null, // 店铺信息
				hasStore: false // 是否有店铺
			}
		},
		computed: {
			name() {
				return this.$store.state.user.name || '';
			},
			avatar() {
				return this.$store.state.user.avatar || '';
			},
			userId() {
				return this.$store.state.user.userId || '';
			},
			// 是否已登录
			isLoggedIn() {
				return !!(this.userId && this.$store.state.user.token);
			},
			// 店铺显示文本
			storeDisplayText() {
				if (!this.isLoggedIn) {
					return '请先登录后开通店铺';
				}
				return this.hasStore ? this.storeInfo?.storeName : '您暂未开通店铺'
			},
			// 按钮文本
			storeButtonText() {
				if (!this.isLoggedIn) {
					return '立即登录';
				}
				return this.hasStore ? '店铺管理' : '立即开通'
			}
		},
		onLoad() {
			this.setStatusBarHeight();
			this.getStoreInfo()
		},

		onShow() {
			// 页面显示时检查用户状态
			if (!this.userId) {
				// 如果没有用户ID，清除店铺信息
				this.storeInfo = null;
				this.hasStore = false;
			}

			// 同步tabBar状态
			this.$nextTick(() => {
				if (this.$refs.customTabbar) {
					this.$refs.customTabbar.syncCurrentPageState();
				}
			});
		},

		watch: {
			// 监听用户ID变化，当用户退出登录时清除店铺信息
			userId(newVal, oldVal) {
				if (!newVal && oldVal) {
					// 用户退出登录，清除店铺信息
					this.storeInfo = null;
					this.hasStore = false;
				} else if (newVal && !oldVal) {
					// 用户登录，获取店铺信息
					this.getStoreInfo();
				}
			}
		},
		methods: {
			// 处理头像点击
			handleAvatarClick() {
				if (this.isLoggedIn) {
					this.handleToAvatar();
				} else {
					this.handleLogin();
				}
			},

			// 跳转到登录页
			handleLogin() {
				uni.navigateTo({
					url: '/pages/login'
				});
			},

			// 处理店铺相关操作
			handleStoreAction() {
				if (!this.isLoggedIn) {
					this.handleLogin();
					return;
				}
				this.handleOpenStore();
			},

			// 获取店铺信息
			getStoreInfo() {
				if (!this.userId) return
				
				listStoreInfo({ userId: this.userId }).then(res => {
					if (res.code === 200 && res.rows && res.rows.length > 0) {
						this.hasStore = true
						this.storeInfo = res.rows[0]
					} else {
						this.hasStore = false
						this.storeInfo = null
					}
				}).catch(error => {
					console.error('获取店铺信息失败:', error)
					this.hasStore = false
					this.storeInfo = null
				})
			},
			// 点击头像
			handleToAvatar() {
				this.$tab.navigateTo('/pages/mine/avatar/index')
			},
			// 点击编辑
			handleToEditInfo() {
				this.$tab.navigateTo('/pages/mine/info/edit')
			},
			// 点击开通店铺或店铺管理
			handleOpenStore() {
				if (this.hasStore) {
					// 跳转到店铺管理页面
					this.$tab.navigateTo('/pages/mine/mangerStore/mangerStore')
				} else {
					// 跳转到店铺信息页面
					this.$tab.navigateTo('/pages/mine/storeInfo/storeInfo')
				}
			},
			// 处理菜单项点击
			handleMenuClick(item) {
				// 退出登录和联系客服不需要登录验证
				if (item.action) {
					if (item.action === 'logout') {
						if (!this.isLoggedIn) {
							uni.showToast({
								title: '您还未登录',
								icon: 'none'
							});
							return;
						}
						this.handleLogout();
					} else if (item.action === 'contact') {
						this.handleContact();
					}
				} else if (item.path) {
					// 其他菜单项需要登录后才能访问
					if (!this.isLoggedIn) {
						uni.showModal({
							title: '提示',
							content: '请先登录后使用此功能',
							confirmText: '去登录',
							cancelText: '取消',
							success: (res) => {
								if (res.confirm) {
									this.handleLogin();
								}
							}
						});
						return;
					}
					this.$tab.navigateTo(item.path);
				}
			},
			// 联系客服
			handleContact() {
				// #ifdef MP-WEIXIN
				// 微信小程序环境下，使用button组件的open-type="contact"更简单可靠
				// 这里我们跳转到一个专门的客服页面
				uni.navigateTo({
					url: '/pages/mine/contact'
				});
				// #endif

				// #ifndef MP-WEIXIN
				// 非微信小程序环境下的处理
				uni.showModal({
					title: '联系客服',
					content: '请在微信小程序中使用客服功能',
					showCancel: false
				});
				// #endif
			},
			// 退出登录
			handleLogout() {
				this.$modal.confirm('确定注销并退出系统吗？').then(() => {
					this.$modal.loading('退出中...');

					// 立即清除页面显示状态，不等待store
					this.storeInfo = null;
					this.hasStore = false;

					// 强制清除store状态
					this.$store.commit('SET_TOKEN', '');
					this.$store.commit('SET_NAME', '');
					this.$store.commit('SET_AVATAR', '');
					this.$store.commit('SET_USER_ID', '');
					this.$store.commit('SET_ROLES', []);
					this.$store.commit('SET_PERMISSIONS', []);

					// 清除本地存储
					uni.removeStorageSync('App-Token');
					uni.removeStorageSync('storage_data');

					// 强制更新页面
					this.$forceUpdate();

					// 调用后端退出接口（异步，不阻塞流程）
					this.$store.dispatch('LogOut').catch(error => {
						console.warn('后端退出接口调用失败:', error);
					});

					this.$modal.closeLoading();

					// 立即跳转
					this.$tab.reLaunch('/pages/index');
				})
			},

			// tabBar切换事件
			onTabChange(e) {
				console.log('Tab切换到:', e.index, e.pagePath);
			}
		}
	}
</script>

<style lang="scss">
	page {
		/* 将渐变背景应用于整个页面 */
		background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
		height: 100vh;
	}

	.mine-container {
		/* 增加顶部内边距以适配状态栏 */
		padding-top: var(--status-bar-height);
		padding-bottom: 120rpx; /* 为自定义tabBar预留空间 */
	}
	
	.profile-section {
		display: flex;
		align-items: center;
		padding: 20rpx 40rpx; /* 控制个人信息区域的内边距 */

		.avatar {
			width: 110rpx;
			height: 110rpx;
			border-radius: 50%;
			border: 4rpx solid #DD1A21;
		}

		.user-details {
			margin-left: 20rpx;
			display: flex;
			align-items: center;

			.user-name {
				font-size: 34rpx;
				font-weight: 500;
				color: #333;
			}

			.login-btn {
				font-size: 28rpx;
				font-weight: 500;
				color: #2979ff;
				padding: 8rpx 20rpx;
				background-color: rgba(41, 121, 255, 0.1);
				border-radius: 20rpx;
				border: 1px solid #2979ff;
			}

			.edit-btn {
				margin-left: 20rpx;
				font-size: 24rpx;
				color: #999;
				background-color: #FEE6E7;
				padding: 4rpx 16rpx;
				border-radius: 20rpx;
			}
		}
	}

	.card {
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 30rpx 30rpx 0;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
	}
	
	.store-card {
		/* 修改为flex布局以容纳图片 */
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.info-content {
			display: flex;
			flex-direction: column;
			align-items: flex-start;

			.title-line {
				display: flex;
				align-items: center;
				.title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
					margin-left: 10rpx;
				}
			}
			
			.subtitle {
				display: block;
				font-size: 26rpx;
				color: #999;
				margin: 10rpx 0 20rpx 0;
			}
			
			.open-btn {
				background-color: #DD1A21;
				color: #fff;
				font-size: 26rpx;
				border-radius: 30rpx;
				padding: 0 30rpx;
				height: 60rpx;
				line-height: 60rpx;
				display: inline-block; 
				margin: 0;
				&::after {
					border: none;
				}
			}
		}

		/* 新增图片样式 */
		.store-3d-image {
			width: 180rpx;
			height: 180rpx;
		}
	}
	
	.menu-list {
		padding: 10rpx 30rpx;
		
		.menu-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 0;
			border-bottom: 1px solid #F5F5F5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.menu-item-left {
				display: flex;
				align-items: center;
				
				.menu-label {
					margin-left: 20rpx;
					font-size: 28rpx;
					color: #333;
				}
			}
		}
	}
</style>