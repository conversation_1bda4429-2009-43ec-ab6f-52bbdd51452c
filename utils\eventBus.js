/**
 * 全局事件总线 - 用于页面间通讯
 */
class EventBus {
	constructor() {
		this.events = {};
	}

	// 监听事件
	on(eventName, callback) {
		if (!this.events[eventName]) {
			this.events[eventName] = [];
		}
		this.events[eventName].push(callback);
	}

	// 触发事件
	emit(eventName, data) {
		if (this.events[eventName]) {
			this.events[eventName].forEach(callback => {
				try {
					callback(data);
				} catch (error) {
					console.error('EventBus callback error:', error);
				}
			});
		}
	}

	// 移除事件监听
	off(eventName, callback) {
		if (this.events[eventName]) {
			if (callback) {
				// 移除特定回调
				this.events[eventName] = this.events[eventName].filter(cb => cb !== callback);
			} else {
				// 移除所有回调
				delete this.events[eventName];
			}
		}
	}

	// 只监听一次
	once(eventName, callback) {
		const onceCallback = (data) => {
			callback(data);
			this.off(eventName, onceCallback);
		};
		this.on(eventName, onceCallback);
	}
}

// 创建全局实例
const eventBus = new EventBus();

export default eventBus;
