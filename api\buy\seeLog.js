import request from '@/utils/request'

// 查询访问记录列表
export function listSeeLog(query) {
  return request({
    url: '/buy/seeLog/list',
    method: 'get',
    params: query
  })
}

// 查询访问记录详细
export function getSeeLog(id) {
  return request({
    url: '/buy/seeLog/' + id,
    method: 'get'
  })
}

// 新增访问记录
export function addSeeLog(data) {
  return request({
    url: '/buy/seeLog',
    method: 'post',
    data: data
  })
}

// 修改访问记录
export function updateSeeLog(data) {
  return request({
    url: '/buy/seeLog',
    method: 'put',
    data: data
  })
}

// 删除访问记录
export function delSeeLog(id) {
  return request({
    url: '/buy/seeLog/' + id,
    method: 'delete'
  })
}
