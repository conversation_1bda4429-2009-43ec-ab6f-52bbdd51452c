import request from '@/utils/request'

// 查询商店信息(含审核流程)列表
export function listStoreInfo(query) {
  return request({
    url: '/buy/storeInfo/list',
    method: 'get',
    params: query
  })
}

//搜索接口
export function search(query) {
  return request({
    url: '/buy/storeInfo/search',
    method: 'get',
    params: query
  })
}

// 查询商店信息(含审核流程)详细
export function getStoreInfo(storeId) {
  return request({
    url: '/buy/storeInfo/' + storeId,
    method: 'get'
  })
}

// 新增商店信息(含审核流程)
export function addStoreInfo(data) {
  return request({
    url: '/buy/storeInfo',
    method: 'post',
    data: data
  })
}

// 修改商店信息(含审核流程)
export function updateStoreInfo(data) {
  return request({
    url: '/buy/storeInfo',
    method: 'put',
    data: data
  })
}

// 删除商店信息(含审核流程)
export function delStoreInfo(storeId) {
  return request({
    url: '/buy/storeInfo/' + storeId,
    method: 'delete'
  })
}
