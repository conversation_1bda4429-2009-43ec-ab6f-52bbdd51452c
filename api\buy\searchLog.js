import request from '@/utils/request'

// 查询搜索记录列表
export function listSearchLog(query) {
  return request({
    url: '/buy/searchLog/list',
    method: 'get',
    params: query
  })
}

// 查询搜索记录详细
export function getSearchLog(id) {
  return request({
    url: '/buy/searchLog/' + id,
    method: 'get'
  })
}

// 新增搜索记录
export function addSearchLog(data) {
  return request({
    url: '/buy/searchLog',
    method: 'post',
    data: data
  })
}

// 修改搜索记录
export function updateSearchLog(data) {
  return request({
    url: '/buy/searchLog',
    method: 'put',
    data: data
  })
}

// 删除搜索记录
export function delSearchLog(id) {
  return request({
    url: '/buy/searchLog/' + id,
    method: 'delete'
  })
}
