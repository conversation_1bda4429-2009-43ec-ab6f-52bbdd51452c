import request from '@/utils/request'

// 查询商户向用户转账记录列表
export function listMerchantTransfer(query) {
  return request({
    url: '/buy/merchantTransfer/list',
    method: 'get',
    params: query
  })
}

// 查询商户向用户转账记录详细
export function getMerchantTransfer(transferId) {
  return request({
    url: '/buy/merchantTransfer/' + transferId,
    method: 'get'
  })
}

// 新增商户向用户转账记录
export function addMerchantTransfer(data) {
  return request({
    url: '/buy/merchantTransfer',
    method: 'post',
    data: data
  })
}

// 修改商户向用户转账记录
export function updateMerchantTransfer(data) {
  return request({
    url: '/buy/merchantTransfer',
    method: 'put',
    data: data
  })
}

// 删除商户向用户转账记录
export function delMerchantTransfer(transferId) {
  return request({
    url: '/buy/merchantTransfer/' + transferId,
    method: 'delete'
  })
}
