import request from '@/utils/request'

// 获取店铺余额
export function getStoreBalance(storeId) {
  return request({
    url: '/buy/store/balance',
    method: 'get',
    params: {
      storeId
    }
  })
}

// 创建充值订单
export function createRechargeOrder(data) {
  return request({
    url: '/buy/recharge/create',
    method: 'post',
    data: data
  })
}

// 获取充值记录
export function getRechargeRecords(params) {
  return request({
    url: '/buy/recharge/list',
    method: 'get',
    params: params
  })
}

// 查询充值订单状态
export function getRechargeOrderStatus(orderId) {
  return request({
    url: '/buy/recharge/status',
    method: 'get',
    params: {
      orderId
    }
  })
}
