<template>
	<view class="page-container">
		<!-- 顶部操作栏 -->
		<view class="header-section">
			<u-button type="primary" @click="showAddDialog">
				<u-icon name="plus" size="16" style="margin-right: 4px;"></u-icon>
				新增分类
			</u-button>
		</view>

		<!-- 分类列表 -->
		<view class="category-list">
			<view v-if="categoryList.length > 0">
				<view 
					v-for="(item, index) in categoryList" 
					:key="item.categoryId" 
					class="category-item"
				>
					<view class="category-info">
						<view class="category-name">{{ item.categoryName }}</view>
						<view class="category-meta">
							<text class="order-num">排序：{{ item.orderNum }}</text>
							<text class="create-time">{{ formatTime(item.createTime) }}</text>
						</view>

					</view>
					<view class="category-actions">
						<u-button 
							type="primary" 
							size="mini" 
							plain 
							@click="showEditDialog(item)"
						>编辑</u-button>
						<u-button 
							type="error" 
							size="mini" 
							plain 
							@click="handleDelete(item, index)"
							style="margin-left: 8px;"
						>删除</u-button>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-else-if="!loading" class="empty-container">
				<u-empty
					mode="list"
					text="暂无分类数据"
					icon="http://cdn.uviewui.com/uview/empty/list.png"
				>
					<u-button 
						type="primary" 
						text="添加分类" 
						@click="showAddDialog"
						custom-style="margin-top: 20px"
					></u-button>
				</u-empty>
			</view>
		</view>

		<!-- 新增/编辑弹窗 -->
		<u-modal 
			:show="showDialog" 
			:title="dialogTitle"
			@confirm="handleSubmit"
			@cancel="handleCancel"
			:show-cancel-button="true"
			confirm-text="确定"
			cancel-text="取消"
		>
			<view class="dialog-content">
				<u-form :model="dialogForm" ref="dialogFormRef" label-width="80">
					<u-form-item label="分类名称" prop="categoryName" required>
						<u-input 
							v-model="dialogForm.categoryName" 
							placeholder="请输入分类名称"
							:maxlength="50"
						/>
					</u-form-item>
					<u-form-item label="显示顺序" prop="orderNum">
						<u-input 
							v-model="dialogForm.orderNum" 
							placeholder="请输入显示顺序"
							type="number"
						/>
					</u-form-item>

				</u-form>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import { listProductCategory, getProductCategory, addProductCategory, updateProductCategory, delProductCategory } from "@/api/buy/productCategory.js";
	import { listStoreInfo } from "@/api/buy/storeInfo.js";

	export default {
		data() {
			return {
				userId: this.$store.state.user.userId,
				storeId: null,
				loading: false,
				categoryList: [],
				
				// 弹窗相关
				showDialog: false,
				dialogTitle: '新增分类',
				isEdit: false,
				dialogForm: {
					categoryId: null,
					storeId: null,
					categoryName: '',
					orderNum: 0
				},
				
				rules: {
					'categoryName': { 
						type: 'string', 
						required: true, 
						message: '请输入分类名称', 
						trigger: ['blur', 'change'] 
					}
				}
			}
		},
		onLoad() {
			this.getStoreInfo();
		},
		onReady() {
			this.$refs.dialogFormRef && this.$refs.dialogFormRef.setRules(this.rules);
		},
		methods: {
			// 获取店铺信息
			async getStoreInfo() {
				try {
					this.$modal.loading('加载中...');
					const res = await listStoreInfo({ userId: this.userId, pageNum: 1, pageSize: 1 });
					this.$modal.closeLoading();
					
					if (res.code === 200 && res.rows.length > 0) {
						this.storeId = res.rows[0].storeId;
						this.dialogForm.storeId = this.storeId;
						await this.getCategoryList();
					} else {
						this.$modal.msgError('请先完成店铺入驻');
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				} catch (error) {
					this.$modal.closeLoading();
					this.$modal.msgError('获取店铺信息失败');
				}
			},

			// 获取分类列表
			async getCategoryList() {
				if (this.loading) return;
				
				this.loading = true;
				try {
					const res = await listProductCategory({ 
						storeId: this.storeId, 
						pageSize: 1000 
					});
					this.loading = false;

					if (res.code === 200) {
						this.categoryList = res.rows || [];
						// 按排序号排序
						this.categoryList.sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0));
					} else {
						this.$modal.msgError(res.msg || '获取分类列表失败');
					}
				} catch (error) {
					this.loading = false;
					this.$modal.msgError('获取分类列表失败');
				}
			},

			// 显示新增弹窗
			showAddDialog() {
				this.dialogTitle = '新增分类';
				this.isEdit = false;
				this.dialogForm = {
					categoryId: null,
					storeId: this.storeId,
					categoryName: '',
					orderNum: this.getNextOrderNum()
				};
				this.showDialog = true;
			},

			// 显示编辑弹窗
			showEditDialog(item) {
				this.dialogTitle = '编辑分类';
				this.isEdit = true;
				this.dialogForm = { ...item };
				this.showDialog = true;
			},

			// 获取下一个排序号
			getNextOrderNum() {
				if (this.categoryList.length === 0) return 1;
				const maxOrderNum = Math.max(...this.categoryList.map(item => item.orderNum || 0));
				return maxOrderNum + 1;
			},

			// 提交表单
			handleSubmit() {
				// 验证表单
				if (!this.dialogForm.categoryName.trim()) {
					this.$modal.msgError('请输入分类名称');
					return;
				}

				// 检查分类名称是否重复
				const existingCategory = this.categoryList.find(item => 
					item.categoryName === this.dialogForm.categoryName.trim() && 
					item.categoryId !== this.dialogForm.categoryId
				);
				if (existingCategory) {
					this.$modal.msgError('分类名称已存在');
					return;
				}

				this.$modal.loading(this.isEdit ? '保存中...' : '添加中...');
				const api = this.isEdit ? updateProductCategory : addProductCategory;
				const successMsg = this.isEdit ? '修改成功' : '添加成功';

				// 确保orderNum是数字
				this.dialogForm.orderNum = parseInt(this.dialogForm.orderNum) || 0;

				api(this.dialogForm).then(response => {
					this.$modal.closeLoading();
					if (response.code === 200) {
						this.$modal.msgSuccess(successMsg);
						this.showDialog = false;
						this.getCategoryList();
					} else {
						this.$modal.msgError(response.msg || '操作失败');
					}
				}).catch(() => {
					this.$modal.closeLoading();
					this.$modal.msgError('操作失败');
				});
			},

			// 取消弹窗
			handleCancel() {
				this.showDialog = false;
			},

			// 删除分类
			handleDelete(item, index) {
				uni.showModal({
					title: '确认删除',
					content: `确定要删除分类"${item.categoryName}"吗？删除后该分类下的商品将变为未分类状态。`,
					success: async (res) => {
						if (res.confirm) {
							try {
								this.$modal.loading('删除中...');
								const result = await delProductCategory(item.categoryId);
								this.$modal.closeLoading();
								
								if (result.code === 200) {
									this.$modal.msgSuccess('删除成功');
									this.categoryList.splice(index, 1);
								} else {
									this.$modal.msgError(result.msg || '删除失败');
								}
							} catch (error) {
								this.$modal.closeLoading();
								this.$modal.msgError('删除失败');
							}
						}
					}
				});
			},

			// 格式化时间
			formatTime(time) {
				if (!time) return '';
				const date = new Date(time);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header-section {
		background-color: #fff;
		padding: 15px;
		border-bottom: 1px solid #f0f0f0;
		display: flex;
		justify-content: flex-end;
	}

	.category-list {
		padding: 0 15px;
	}

	.category-item {
		background-color: #fff;
		border-radius: 8px;
		padding: 15px;
		margin: 12px 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
		
		.category-info {
			flex: 1;
			
			.category-name {
				font-size: 16px;
				font-weight: 600;
				color: #303133;
				margin-bottom: 8px;
			}
			
			.category-meta {
				display: flex;
				align-items: center;
				margin-bottom: 6px;
				
				.order-num {
					font-size: 13px;
					color: #909399;
					margin-right: 15px;
				}
				
				.create-time {
					font-size: 12px;
					color: #c0c4cc;
				}
			}
			

		}
		
		.category-actions {
			display: flex;
			flex-shrink: 0;
		}
	}

	.empty-container {
		padding-top: 100px;
	}

	.dialog-content {
		padding: 20px 0;
	}
</style>
