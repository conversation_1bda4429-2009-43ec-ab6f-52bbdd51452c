<template>
	<view class="detail-container">
		<scroll-view class="content-scroll" scroll-y="true">
			<!-- 帖子内容 -->
			<view v-if="postDetail.postId" class="post-section">
				<!-- 用户信息 -->
				<view class="post-header">
					<image 
						:src="getUserAvatar(postDetail.userAvatar)" 
						class="user-avatar"
						mode="aspectFill"
					></image>
					<view class="user-info">
						<text class="user-name">{{ postDetail.nickName || '匿名用户' }}</text>
						<text class="post-time">{{ formatTime(postDetail.createTime) }}</text>
					</view>
				</view>
				
				<!-- 帖子内容 -->
				<view class="post-content">
					<text class="content-text">{{ postDetail.content }}</text>
				</view>
				
				<!-- 图片展示 -->
				<view v-if="postDetail.images" class="post-images">
					<view class="image-list">
						<image
							v-for="(image, index) in postImageList"
							:key="index"
							:src="getImageUrl(image)"
							class="large-image"
							mode="widthFix"
							@click="previewImage(postImageList, index)"
						></image>
					</view>
				</view>
				
				<!-- 视频展示 -->
				<view v-if="postDetail.videoUrl" class="post-video">
					<video 
						:src="getImageUrl(postDetail.videoUrl)"
						class="video-player"
						controls
					></video>
				</view>
			</view>
			
			<!-- 评论列表 -->
			<view class="comments-section">
				<view class="comments-header">
					<text class="comments-title">评论 ({{ commentList.length }})</text>
				</view>
				
				<view class="comments-list">
					<view 
						v-for="comment in commentList" 
						:key="comment.commentId"
						class="comment-item"
					>
						<!-- 主评论 -->
						<view class="comment-main">
							<image 
								:src="getUserAvatar(comment.userAvatar)" 
								class="comment-avatar"
								mode="aspectFill"
							></image>
							<view class="comment-content">
								<view class="comment-header">
									<text class="comment-user">{{ comment.nickName || '匿名用户' }}</text>
									<text class="comment-time">{{ formatTime(comment.createTime) }}</text>
								</view>
								<text class="comment-text">{{ comment.content }}</text>
								<view class="comment-actions">
									<text class="action-btn" @click="replyComment(comment)">回复</text>
									<text 
										v-if="canDeleteComment(comment)"
										class="action-btn delete-btn" 
										@click="deleteComment(comment.commentId)"
									>删除</text>
								</view>
							</view>
						</view>
						
						<!-- 回复列表 -->
						<view v-if="comment.replies && comment.replies.length > 0" class="replies-list">
							<view 
								v-for="reply in comment.replies" 
								:key="reply.commentId"
								class="reply-item"
							>
								<image 
									:src="getUserAvatar(reply.userAvatar)" 
									class="reply-avatar"
									mode="aspectFill"
								></image>
								<view class="reply-content">
									<view class="reply-header">
										<text class="reply-user">{{ reply.nickName || '匿名用户' }}</text>
										<text v-if="reply.replyToUserName" class="reply-to">
											回复 {{ reply.replyToUserName }}
										</text>
										<text class="reply-time">{{ formatTime(reply.createTime) }}</text>
									</view>
									<text class="reply-text">{{ reply.content }}</text>
									<view class="reply-actions">
										<text class="action-btn" @click="replyComment(reply, comment)">回复</text>
										<text 
											v-if="canDeleteComment(reply)"
											class="action-btn delete-btn" 
											@click="deleteComment(reply.commentId)"
										>删除</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 加载更多评论 -->
					<view v-if="hasMoreComments" class="load-more" @click="loadMoreComments">
						<text>加载更多评论...</text>
					</view>
					<view v-else-if="commentList.length === 0" class="empty-comments">
						<text>暂无评论，快来抢沙发吧~</text>
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<view class="action-btn" @click="toggleFavorite">
				<text class="action-icon">{{ isFavorited ? '❤️' : '🤍' }}</text>
				<text class="action-text" :style="{ color: isFavorited ? '#ff6b6b' : '#666' }">{{ isFavorited ? '已收藏' : '收藏' }}</text>
			</view>
			<view class="comment-input-container">
				<input 
					v-model="commentContent"
					class="comment-input"
					:placeholder="replyTarget ? `回复 ${replyTarget.nickName}` : '写评论...'"
					@focus="onInputFocus"
					@blur="onInputBlur"
				/>
			</view>
			<view class="send-btn" :class="{ active: commentContent.trim() }" @click="sendComment">
				<text class="send-text">发送</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getCirclePost } from '@/api/buy/circlePost'
import { listCircleComment, addCircleComment, delCircleComment } from '@/api/buy/circleComment'
import { listFavorite, addFavorite, delFavorite } from '@/api/buy/favorite'
import config from '@/config'
import eventBus from '@/utils/eventBus'

export default {
	data() {
		return {
			postId: null,
			postDetail: {},
			commentList: [],
			commentContent: '',
			replyTarget: null, // 回复目标
			parentComment: null, // 父评论
			isFavorited: false,
			favoriteId: null, // 收藏记录ID
			hasMoreComments: true,
			commentPageNum: 1,
			commentPageSize: 20,
			baseUrl: config.baseUrl
		}
	},
	computed: {
		// 帖子图片列表
		postImageList() {
			return this.getImageList(this.postDetail.images);
		}
	},
	onLoad(options) {
		if (options.postId) {
			this.postId = options.postId;
			this.getPostDetail();
			this.getCommentList();
			this.checkFavoriteStatus();
		}
	},
	methods: {
		// 获取帖子详情
		async getPostDetail() {
			try {
				const response = await getCirclePost(this.postId);
				if (response.code === 200) {
					this.postDetail = response.data || {};
				}
			} catch (error) {
				console.error('获取帖子详情失败:', error);
				uni.showToast({
					title: '获取帖子详情失败',
					icon: 'none'
				});
			}
		},
		
		// 获取评论列表
		async getCommentList(loadMore = false) {
			try {
				const params = {
					postId: this.postId,
					pageNum: loadMore ? this.commentPageNum : 1,
					pageSize: this.commentPageSize
				};
				
				const response = await listCircleComment(params);
				if (response.code === 200) {
					const newComments = response.rows || [];
					
					// 处理评论层级关系
					const processedComments = this.processComments(newComments);
					
					if (loadMore) {
						this.commentList = [...this.commentList, ...processedComments];
					} else {
						this.commentList = processedComments;
						this.commentPageNum = 1;
					}
					
					this.hasMoreComments = newComments.length === this.commentPageSize;
					if (loadMore && newComments.length > 0) {
						this.commentPageNum++;
					}
				}
			} catch (error) {
				console.error('获取评论列表失败:', error);
			}
		},
		
		// 处理评论层级关系
		processComments(comments) {
			const commentMap = new Map();
			const rootComments = [];
			
			// 先创建所有评论的映射
			comments.forEach(comment => {
				comment.replies = [];
				commentMap.set(comment.commentId, comment);
			});
			
			// 建立父子关系
			comments.forEach(comment => {
				if (comment.parentId === 0) {
					// 根评论
					rootComments.push(comment);
				} else {
					// 回复评论
					const parentComment = commentMap.get(comment.parentId);
					if (parentComment) {
						parentComment.replies.push(comment);
					}
				}
			});
			
			return rootComments;
		},
		
		// 加载更多评论
		loadMoreComments() {
			if (this.hasMoreComments) {
				this.getCommentList(true);
			}
		},
		
		// 回复评论
		replyComment(comment, parentComment = null) {
			this.replyTarget = comment;
			this.parentComment = parentComment || comment;
			
			// 聚焦输入框
			this.$nextTick(() => {
				const input = uni.createSelectorQuery().select('.comment-input');
				input.focus();
			});
		},
		
		// 发送评论
		async sendComment() {
			if (!this.commentContent.trim()) return;
			
			try {
				const commentData = {
					postId: this.postId,
					content: this.commentContent.trim(),
					parentId: this.replyTarget ? this.parentComment.commentId : 0,
					replyToUserId: this.replyTarget ? this.replyTarget.userId : null
				};
				
				const response = await addCircleComment(commentData);
				if (response.code === 200) {
					uni.showToast({
						title: '评论成功',
						icon: 'success'
					});
					
					// 清空输入框和回复目标
					this.commentContent = '';
					this.replyTarget = null;
					this.parentComment = null;
					
					// 刷新评论列表
					this.getCommentList();
				} else {
					uni.showToast({
						title: response.msg || '评论失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('发送评论失败:', error);
				uni.showToast({
					title: '评论失败',
					icon: 'none'
				});
			}
		},
		
		// 删除评论
		async deleteComment(commentId) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条评论吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await delCircleComment(commentId);
							if (response.code === 200) {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								// 刷新评论列表
								this.getCommentList();
							} else {
								uni.showToast({
									title: response.msg || '删除失败',
									icon: 'none'
								});
							}
						} catch (error) {
							console.error('删除评论失败:', error);
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 判断是否可以删除评论
		canDeleteComment(comment) {
			// 这里需要根据实际业务逻辑判断
			// 通常是评论作者本人或管理员可以删除
			const currentUserId = this.$store.state.user.userId;
			return currentUserId && (currentUserId == comment.userId);
		},
		
		// 切换收藏状态
		async toggleFavorite() {
			// 检查用户是否登录
			if (!this.$store.state.user || !this.$store.state.user.userId) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				return;
			}

			try {
				if (this.isFavorited) {
					// 取消收藏
					await this.removeFavorite();
				} else {
					// 添加收藏
					await this.addToFavorite();
				}
			} catch (error) {
				console.error('收藏操作失败:', error);
				uni.showToast({
					title: '操作失败，请稍后重试',
					icon: 'none'
				});
			}
		},
		
		// 输入框聚焦
		onInputFocus() {
			// 可以在这里处理键盘弹起的逻辑
		},
		
		// 输入框失焦
		onInputBlur() {
			// 延迟清除回复目标，避免点击发送按钮时被清除
			setTimeout(() => {
				if (!this.commentContent.trim()) {
					this.replyTarget = null;
					this.parentComment = null;
				}
			}, 200);
		},
		
		// 获取用户头像
		getUserAvatar(avatar) {
			if (!avatar) {
				return '/static/images/profile.jpg';
			}
			return this.getImageUrl(avatar);
		},
		
		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '';
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			return this.baseUrl + imagePath;
		},
		
		// 获取图片列表
		getImageList(images) {
			if (!images) return [];
			return images.split(',').filter(img => img.trim());
		},
		

		
		// 预览图片
		previewImage(images, current) {
			const urls = images.map(img => this.getImageUrl(img));
			uni.previewImage({
				urls: urls,
				current: current
			});
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return '';
			const date = new Date(time);
			const now = new Date();
			const diff = now - date;

			if (diff < 60000) { // 1分钟内
				return '刚刚';
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前';
			} else if (diff < 86400000) { // 1天内
				return Math.floor(diff / 3600000) + '小时前';
			} else if (diff < 604800000) { // 1周内
				return Math.floor(diff / 86400000) + '天前';
			} else {
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			}
		},

		// 检查收藏状态
		async checkFavoriteStatus() {
			if (!this.$store.state.user || !this.$store.state.user.userId) {
				return;
			}

			try {
				const response = await listFavorite({
					userId: this.$store.state.user.userId,
					targetId: this.postId,
					targetType: 'post'
				});

				if (response.code === 200 && response.rows && response.rows.length > 0) {
					this.isFavorited = true;
					this.favoriteId = response.rows[0].favoriteId;
				} else {
					this.isFavorited = false;
					this.favoriteId = null;
				}
			} catch (error) {
				console.error('检查收藏状态失败:', error);
			}
		},

		// 添加收藏
		async addToFavorite() {
			const response = await addFavorite({
				userId: this.$store.state.user.userId,
				targetId: this.postId,
				targetType: 'post'
			});

			if (response.code === 200) {
				this.isFavorited = true;
				this.favoriteId = response.data?.favoriteId;
				uni.showToast({
					title: '收藏成功',
					icon: 'success'
				});

				// 触发收藏变化事件
				eventBus.emit('favoriteChanged', {
					action: 'add',
					targetType: 'post',
					targetId: this.postId
				});
			} else {
				throw new Error(response.msg || '收藏失败');
			}
		},

		// 取消收藏
		async removeFavorite() {
			if (!this.favoriteId) {
				// 如果没有favoriteId，重新查询获取
				await this.checkFavoriteStatus();
				if (!this.favoriteId) {
					throw new Error('未找到收藏记录');
				}
			}

			const response = await delFavorite(this.favoriteId);

			if (response.code === 200) {
				this.isFavorited = false;
				this.favoriteId = null;
				uni.showToast({
					title: '取消收藏成功',
					icon: 'success'
				});

				// 触发收藏变化事件
				eventBus.emit('favoriteChanged', {
					action: 'remove',
					targetType: 'post',
					targetId: this.postId
				});
			} else {
				throw new Error(response.msg || '取消收藏失败');
			}
		}
	}
}
</script>

<style lang="scss">
.detail-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
}

.content-scroll {
	flex: 1;
	padding-bottom: 120rpx; // 为底部操作栏留出空间
}

.post-section {
	background-color: #fff;
	padding: 24rpx;
	margin-bottom: 20rpx;
	
	.post-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		.user-avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 40rpx;
			margin-right: 20rpx;
		}
		
		.user-info {
			flex: 1;
			
			.user-name {
				display: block;
				font-size: 28rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
			}
			
			.post-time {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
	
	.post-content {
		margin-bottom: 20rpx;
		
		.content-text {
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
		}
	}
	
	.post-images {
		margin-bottom: 20rpx;

		.image-list {
			display: flex;
			flex-direction: column;
			gap: 12rpx;

			.large-image {
				width: 100%;
				border-radius: 8rpx;
				max-height: 800rpx;
			}
		}
	}
	
	.post-video {
		.video-player {
			width: 100%;
			height: 400rpx;
			border-radius: 8rpx;
		}
	}
}

.comments-section {
	background-color: #fff;
	padding: 24rpx;

	.comments-header {
		margin-bottom: 20rpx;

		.comments-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
	}

	.comments-list {
		.comment-item {
			margin-bottom: 30rpx;

			.comment-main {
				display: flex;

				.comment-avatar {
					width: 60rpx;
					height: 60rpx;
					border-radius: 30rpx;
					margin-right: 16rpx;
					flex-shrink: 0;
				}

				.comment-content {
					flex: 1;

					.comment-header {
						display: flex;
						align-items: center;
						margin-bottom: 8rpx;

						.comment-user {
							font-size: 26rpx;
							font-weight: 500;
							color: #333;
							margin-right: 16rpx;
						}

						.comment-time {
							font-size: 22rpx;
							color: #999;
						}
					}

					.comment-text {
						font-size: 26rpx;
						color: #333;
						line-height: 1.5;
						margin-bottom: 12rpx;
					}

					.comment-actions {
						display: flex;
						gap: 24rpx;

						.action-btn {
							font-size: 22rpx;
							color: #666;

							&.delete-btn {
								color: #ff4757;
							}
						}
					}
				}
			}

			.replies-list {
				margin-top: 16rpx;
				margin-left: 76rpx;
				padding-left: 20rpx;
				border-left: 2rpx solid #f0f0f0;

				.reply-item {
					display: flex;
					margin-bottom: 20rpx;

					.reply-avatar {
						width: 50rpx;
						height: 50rpx;
						border-radius: 25rpx;
						margin-right: 12rpx;
						flex-shrink: 0;
					}

					.reply-content {
						flex: 1;

						.reply-header {
							display: flex;
							align-items: center;
							margin-bottom: 6rpx;
							flex-wrap: wrap;

							.reply-user {
								font-size: 24rpx;
								font-weight: 500;
								color: #333;
								margin-right: 8rpx;
							}

							.reply-to {
								font-size: 22rpx;
								color: #2979ff;
								margin-right: 8rpx;
							}

							.reply-time {
								font-size: 20rpx;
								color: #999;
							}
						}

						.reply-text {
							font-size: 24rpx;
							color: #333;
							line-height: 1.5;
							margin-bottom: 10rpx;
						}

						.reply-actions {
							display: flex;
							gap: 20rpx;

							.action-btn {
								font-size: 20rpx;
								color: #666;

								&.delete-btn {
									color: #ff4757;
								}
							}
						}
					}
				}
			}
		}

		.load-more, .empty-comments {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 80rpx;

			text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #fff;
	border-top: 1px solid #e5e5e5;
	gap: 16rpx;

	.action-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 8rpx 16rpx;

		.action-icon {
			font-size: 32rpx;
			margin-bottom: 4rpx;
		}

		.action-text {
			font-size: 20rpx;
			color: #666;
		}
	}

	.comment-input-container {
		flex: 1;

		.comment-input {
			width: 100%;
			height: 60rpx;
			padding: 0 20rpx;
			background-color: #f5f5f5;
			border-radius: 30rpx;
			font-size: 26rpx;
			color: #333;
		}
	}

	.send-btn {
		padding: 12rpx 24rpx;
		background-color: #f5f5f5;
		border-radius: 20rpx;

		.send-text {
			font-size: 24rpx;
			color: #999;
		}

		&.active {
			background-color: #2979ff;

			.send-text {
				color: #fff;
			}
		}
	}
}
</style>
