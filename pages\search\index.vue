<template>
	<view class="search-container">
		<!-- 搜索栏 -->
		<view class="search-header">
			<view class="search-bar">
				<view class="search-input-wrapper">
					<uni-icons type="search" size="18" color="#999"></uni-icons>
					<input 
						class="search-input" 
						type="text" 
						v-model="keyword" 
						placeholder="请输入关键词" 
						@confirm="handleSearch"
						focus
					/>
				</view>
				<button class="search-btn" @click="handleSearch">搜索</button>
			</view>
		</view>

		<!-- 分类内容 -->
		<view class="category-content">
			<!-- 左侧一级分类 -->
			<view class="left-categories">
				<view 
					v-for="(category, index) in categories" 
					:key="category.id"
					class="category-item"
					:class="{ active: selectedCategoryIndex === index }"
					@click="selectCategory(index)"
				>
					<text class="category-name">{{ category.label }}</text>
				</view>
			</view>

			<!-- 右侧二级和三级分类 -->
			<view class="right-categories">
				<view v-if="selectedCategory && selectedCategory.children && selectedCategory.children.length > 0">
					<view
						v-for="secondCategory in selectedCategory.children"
						:key="secondCategory.id"
						class="second-category"
					>
						<!-- 二级分类标题 -->
						<view class="second-category-title" @click="handleCategoryClick(secondCategory)">
							{{ secondCategory.label }}
						</view>

						<!-- 三级分类网格 -->
						<view class="third-categories" v-if="secondCategory.children && secondCategory.children.length > 0">
							<view
								v-for="thirdCategory in secondCategory.children"
								:key="thirdCategory.id"
								class="third-category-item"
								@click="handleCategoryClick(thirdCategory)"
							>
								<image
									:src="getImageUrl(thirdCategory.imageUrl)"
									class="category-image"
									mode="aspectFill"
									@error="handleImageError"
								></image>
								<text class="category-label">{{ thirdCategory.label }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view v-else-if="categories.length > 0" class="empty-category">
					<uni-icons type="info" size="60" color="#ccc"></uni-icons>
					<text class="empty-text">该分类暂无子分类</text>
				</view>

				<!-- 加载状态 -->
				<view v-else class="loading-category">
					<uni-icons type="spinner-cycle" size="40" color="#999"></uni-icons>
					<text class="loading-text">加载中...</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { treeCategory } from '@/api/buy/category'
	import config from '@/config'

	export default {
		data() {
			return {
				keyword: '',
				cityInfo: null, // 从首页传递过来的城市信息
				categories: [],
				selectedCategoryIndex: 0,
				baseUrl: config.baseUrl
			}
		},
		
		computed: {
			selectedCategory() {
				return this.categories[this.selectedCategoryIndex] || null;
			}
		},
		
		onLoad(options) {
			// 获取传递过来的城市信息
			if (options.cityInfo) {
				this.cityInfo = JSON.parse(decodeURIComponent(options.cityInfo));
			}
			
			// 获取分类数据
			this.loadCategories();
		},
		
		methods: {
			// 加载分类数据
			async loadCategories() {
				try {
					const res = await treeCategory();
					if (res.code === 200) {
						this.categories = res.data || [];
					}
				} catch (error) {
					console.error('加载分类数据失败:', error);
					uni.showToast({
						title: '加载分类失败',
						icon: 'none'
					});
				}
			},
			
			// 选择一级分类
			selectCategory(index) {
				this.selectedCategoryIndex = index;
			},
			
			// 处理搜索
			handleSearch() {
				if (!this.keyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键词',
						icon: 'none'
					});
					return;
				}

				// 跳转到搜索结果页面
				const params = {
					keyword: encodeURIComponent(this.keyword),
					type: 'keyword'
				};

				if (this.cityInfo) {
					params.cityInfo = encodeURIComponent(JSON.stringify(this.cityInfo));
				}

				const query = Object.keys(params).map(key => `${key}=${params[key]}`).join('&');
				uni.navigateTo({
					url: `/pages/searchResult/searchResult?${query}`
				});
			},
			
			// 处理分类点击
			handleCategoryClick(category) {
				// 跳转到搜索结果页面
				const params = {
					categoryId: category.id,
					categoryName: encodeURIComponent(category.label),
					keyword: encodeURIComponent(category.label), // 使用分类名作为搜索关键词
					type: 'category'
				};

				if (this.cityInfo) {
					params.cityInfo = encodeURIComponent(JSON.stringify(this.cityInfo));
				}

				const query = Object.keys(params).map(key => `${key}=${params[key]}`).join('&');
				uni.navigateTo({
					url: `/pages/searchResult/searchResult?${query}`
				});
			},
			
			// 获取图片完整URL
			getImageUrl(imageUrl) {
				if (!imageUrl) {
					return '/static/images/default-category.png'; // 默认图片
				}
				if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
					return imageUrl;
				}
				return this.baseUrl + imageUrl;
			},

			// 处理图片加载错误
			handleImageError(e) {
				console.log('图片加载失败:', e);
				// 可以在这里设置默认图片
				e.target.src = '/static/images/default-category.png';
			}
		}
	}
</script>

<style lang="scss" scoped>
	.search-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.search-header {
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-bottom: 1px solid #eee;
		
		.search-bar {
			display: flex;
			align-items: center;
			background-color: #f8f8f8;
			border: 1px solid #DD1A21;
			border-radius: 40rpx;
			padding: 0 10rpx;
			height: 80rpx;

			.search-input-wrapper {
				flex: 1;
				display: flex;
				align-items: center;
				padding: 0 20rpx;
				
				.search-input {
					width: 100%;
					font-size: 28rpx;
					margin-left: 10rpx;
				}
			}

			.search-btn {
				background-color: #DD1A21;
				color: #fff;
				height: 64rpx;
				line-height: 64rpx;
				font-size: 28rpx;
				border-radius: 32rpx;
				padding: 0 30rpx;
				border: none;
			}
		}
	}

	.category-content {
		display: flex;
		height: calc(100vh - 120rpx);
	}

	.left-categories {
		width: 200rpx;
		background-color: #fff;
		border-right: 1px solid #eee;
		overflow-y: auto;

		.category-item {
			padding: 30rpx 20rpx;
			border-bottom: 1px solid #f5f5f5;
			text-align: center;
			position: relative;

			&.active {
				background-color: #f5f5f5;

				&::after {
					content: '';
					position: absolute;
					right: 0;
					top: 0;
					bottom: 0;
					width: 3px;
					background-color: #DD1A21;
				}
			}

			.category-name {
				font-size: 26rpx;
				color: #333;
				word-break: break-all;
			}
		}
	}

	.right-categories {
		flex: 1;
		background-color: #fff;
		padding: 20rpx;
		overflow-y: auto;
		
		.second-category {
			margin-bottom: 40rpx;
			
			.second-category-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #DD1A21;
				margin-bottom: 20rpx;
				padding: 10rpx 0;
			}
			
			.third-categories {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				gap: 20rpx;
				
				.third-category-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 20rpx;
					border-radius: 10rpx;
					background-color: #f8f8f8;
					
					.category-image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 10rpx;
						margin-bottom: 10rpx;
					}
					
					.category-label {
						font-size: 24rpx;
						color: #333;
						text-align: center;
					}
				}
			}
		}

		.empty-category,
		.loading-category {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 100rpx 0;

			.empty-text,
			.loading-text {
				font-size: 28rpx;
				color: #999;
				margin-top: 20rpx;
			}
		}
	}
</style>
