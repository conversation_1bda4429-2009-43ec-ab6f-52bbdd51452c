<template>
	<view class="page-container">
		<view class="video-list">
			<view 
				v-for="(video, index) in videoList" 
				:key="index" 
				class="video-item"
			>
				<video 
					:src="video" 
					class="video-player" 
					controls 
					:poster="defaultPoster"
					@play="onVideoPlay(index)"
				></video>
				<view class="video-info">
					<text class="video-title">视频 {{ index + 1 }}</text>
				</view>
			</view>
		</view>
		
		<view v-if="videoList.length === 0" class="empty-state">
			<u-icon name="play-circle" size="80" color="#ccc"></u-icon>
			<text class="empty-text">暂无视频</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				videoList: [],
				title: '店铺视频',
				defaultPoster: ''
			}
		},
		onLoad(options) {
			// 设置导航栏标题
			if (options.title) {
				this.title = decodeURIComponent(options.title);
				uni.setNavigationBarTitle({
					title: this.title
				});
			}
			
			// 解析视频列表
			if (options.videos) {
				try {
					this.videoList = JSON.parse(decodeURIComponent(options.videos));
				} catch (error) {
					console.error('解析视频列表失败:', error);
					this.videoList = [];
				}
			}
		},
		methods: {
			onVideoPlay(index) {
				// 当播放某个视频时，暂停其他视频
				const videoElements = uni.createSelectorQuery().selectAll('.video-player');
				videoElements.exec((res) => {
					if (res && res[0]) {
						res[0].forEach((video, i) => {
							if (i !== index) {
								// 暂停其他视频
								video.pause && video.pause();
							}
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 20rpx;
	}

	.video-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.video-item {
		background-color: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
		
		.video-player {
			width: 100%;
			height: 400rpx;
		}
		
		.video-info {
			padding: 20rpx;
			
			.video-title {
				font-size: 28rpx;
				color: #303133;
				font-weight: 500;
			}
		}
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		
		.empty-text {
			margin-top: 20rpx;
			font-size: 28rpx;
			color: #909399;
		}
	}
</style>
