<template>
	<view class="page-container">
		<view v-if="storeInfo" class="content-wrapper">
			<u-swiper :list="mainImages" height="300" indicator indicatorMode="dot" keyName="url" @click="previewSwiper"></u-swiper>

			<view class="product-header card-section">
				<text class="title">{{ storeInfo.storeName }}</text>
				<view class="info-line">
					<text class="info-item">品牌：{{ storeInfo.brand || '暂无' }}</text>
				</view>
				<view class="info-line" v-if="storeInfo.mainBusiness">
					<text class="info-item">主营：</text>
					<view class="main-business-container">
						<text class="main-business-text" :class="{ 'expanded': isMainBusinessExpanded }">{{ storeInfo.mainBusiness }}</text>
						<view v-if="shouldShowExpandButton" class="expand-btn" @click="toggleMainBusiness">
							<text class="expand-text">{{ isMainBusinessExpanded ? '收起' : '展开' }}</text>
							<u-icon :name="isMainBusinessExpanded ? 'arrow-up' : 'arrow-down'" size="12" color="#999"></u-icon>
						</view>
					</view>
				</view>
			</view>
			
			<u-gap height="10" bgColor="#f5f5f5"></u-gap>

			<!-- 商品展示模块 -->
			<view class="products-section card-section" v-if="productList.length > 0">
				<view class="section-title">
					<u-icon name="grid-fill" color="#fa3534" size="20"></u-icon>
					<text class="title-text">店铺商品</text>
					<view class="more-btn" @click="goToProductList" v-if="productList.length >= 6">
						<text>查看更多</text>
						<u-icon name="arrow-right" size="14" color="#999"></u-icon>
					</view>
				</view>
				<u-grid :border="false" col="3">
					<u-grid-item v-for="(product, index) in displayProducts" :key="product.productId">
						<view class="product-item" @click="goToProductDetail(product)">
							<image :src="getProductImage(product)" class="product-image" mode="aspectFill"></image>
							<text class="product-name">{{ product.productName }}</text>
						</view>
					</u-grid-item>
				</u-grid>
			</view>

			<u-gap height="10" bgColor="#f5f5f5"></u-gap>

			<view class="product-intro card-section" v-if="displayImages.length">
				<view class="section-title">
					<u-icon name="tags-fill" color="#fa3534" size="20"></u-icon>
					<text class="title-text">店铺展示</text>
					<view class="more-btn" @click="goToImageGallery('display')" v-if="displayImages.length > 6">
						<text>查看更多</text>
						<u-icon name="arrow-right" size="14" color="#999"></u-icon>
					</view>
				</view>
				<u-grid :border="false" col="3">
					<u-grid-item v-for="(img, index) in displayImagesLimited" :key="index">
						<image :src="img" class="grid-image" mode="aspectFill" @click="previewImage(img, displayImages)"></image>
					</u-grid-item>
				</u-grid>
			</view>

			<u-gap height="10" bgColor="#f5f5f5"></u-gap>
			
			<view class="merchant-pics card-section" v-if="factoryImages.length">
				<view class="section-title">
					<u-icon name="photo-fill" color="#fa3534" size="20"></u-icon>
					<text class="title-text">库房/工厂</text>
					<view class="more-btn" @click="goToImageGallery('factory')" v-if="factoryImages.length > 6">
						<text>查看更多</text>
						<u-icon name="arrow-right" size="14" color="#999"></u-icon>
					</view>
				</view>
				<u-grid :border="false" col="3">
					<u-grid-item v-for="(img, index) in factoryImagesLimited" :key="index">
						<image :src="img" class="grid-image" mode="aspectFill" @click="previewImage(img, factoryImages)"></image>
					</u-grid-item>
				</u-grid>
			</view>

			<u-gap height="10" bgColor="#f5f5f5"></u-gap>
			
			
			<u-gap height="10" bgColor="#f5f5f5"></u-gap>

			<view class="video-section card-section" v-if="videoList.length > 0">
				<view class="section-title">
					<u-icon name="play-circle-fill" color="#fa3534" size="20"></u-icon>
					<text class="title-text">店铺视频</text>
					<view class="more-btn" @click="goToVideoGallery" v-if="videoList.length > 1">
						<text>查看更多</text>
						<u-icon name="arrow-right" size="14" color="#999"></u-icon>
					</view>
				</view>
				<video class="video-player" :src="videoList[0]" :poster="getFullImageUrl(storeInfo.avatar)" controls></video>
			</view>

			<u-gap height="10" bgColor="#f5f5f5"></u-gap>

			<!-- 访客记录模块 -->
			<view class="visitor-section card-section" v-if="storeInfo && (storeInfo.showVisitorInfo === '1' || storeInfo.showVisitorInfo === 1)">
				<view class="section-title">
					<u-icon name="eye-fill" color="#fa3534" size="20"></u-icon>
					<text class="title-text">访客记录</text>
				</view>
				<view class="visitor-list" v-if="visitorList.length > 0">
					<view class="visitor-row" v-for="(row, rowIndex) in visitorRows" :key="rowIndex">
						<view class="visitor-item" v-for="(visitor, itemIndex) in row" :key="visitor.id">
							<view class="visitor-avatar">
								<image
									:src="visitor.avatar ? getFullImageUrl(visitor.avatar) : '/static/images/default-avatar.png'"
									mode="aspectFill"
								></image>
							</view>
							<view class="visitor-info">
								<text class="visitor-name">{{ visitor.nickName || '匿名用户' }}</text>
								<text class="visitor-time">{{ formatVisitTime(visitor.createTime) }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="visitor-empty" v-else-if="!loadingVisitors">
					<u-empty
						mode="list"
						text="暂无访客记录"
						icon="http://cdn.uviewui.com/uview/empty/data.png"
						marginTop="20"
					></u-empty>
				</view>
				<view class="visitor-loading" v-if="loadingVisitors">
					<u-loading-icon mode="flower"></u-loading-icon>
					<text>加载中...</text>
				</view>
			</view>

			<u-gap height="10" bgColor="#f5f5f5"></u-gap>

			<view class="contact-section card-section">
				<view class="section-title">
					<u-icon name="account-fill" color="#fa3534" size="20"></u-icon>
					<text class="title-text">联系我们</text>
				</view>
				<view class="contact-item">
					<text class="item-label">联系人：</text>
					<text class="item-content">{{ storeInfo.contactPerson }}</text>
				</view>
				<view v-for="(phone, pIndex) in phoneNumbers" :key="pIndex" class="contact-item with-button">
					<view class="item-text-wrapper">
						<text class="item-label">电<text class="placeholder">中</text>话：</text>
						<text class="item-content">{{ phone }}</text>
					</view>
					<button class="contact-btn call-btn" @click="callPhone(phone)">拨打</button>
				</view>
				<view class="contact-item with-button">
					<view class="item-text-wrapper">
						<text class="item-label">地<text class="placeholder">中</text>址：</text>
						<text class="item-content">{{ storeInfo.address }}</text>
					</view>
					<button class="contact-btn nav-btn" @click="openMap">导航</button>
				</view>
				<view class="contact-qr-cards">
					<view class="qr-card" v-if="storeInfo.wechatQrcode">
						<image class="qr-image" :src="getFullImageUrl(storeInfo.wechatQrcode)" mode="aspectFit" @click="previewImage(getFullImageUrl(storeInfo.wechatQrcode), [getFullImageUrl(storeInfo.wechatQrcode)])"></image>
						<text class="qr-text">二维码识别加微信</text>
					</view>
					<view class="qr-card" v-if="firstQualificationImage">
						<image class="qr-image" :src="firstQualificationImage" mode="aspectFit" @click="previewImage(firstQualificationImage, qualificationImages)"></image>
						<text class="qr-text">点击查看资质</text>
					</view>
				</view>
			</view>

		</view>

		<view v-else class="loading-container">
			<u-loading-icon text="加载中..." textSize="16"></u-loading-icon>
		</view>

		<!-- 单选题模块 -->
		<view class="question-section card-section" v-if="storeInfo && storeInfo.question">
			<view class="section-title">
				<u-icon name="question-circle" color="#fa3534" size="20"></u-icon>
				<text class="title-text">店铺问答</text>
			</view>
			<view class="question-content">
				<text class="question-text">{{ storeInfo.question.questionText }}</text>
				<view class="options-list">
					<view
						class="option-item"
						:class="{ 'selected': selectedOptionId === option.optionId }"
						v-for="option in storeInfo.question.options"
						:key="option.optionId"
						@click="selectOption(option.optionId)"
					>
						<view class="option-radio">
							<view class="radio-inner" v-if="selectedOptionId === option.optionId"></view>
						</view>
						<text class="option-text">{{ option.optionText }}</text>
					</view>
				</view>
				<view class="question-actions" v-if="!showResult">
					<button class="submit-btn" :disabled="!selectedOptionId" @click="submitAnswer">
						<text class="submit-text">提交答案</text>
					</button>
				</view>
				<view class="result-message" v-if="showResult && isAnswerCorrect">
					<text class="result-text correct">回答正确！</text>
				</view>
			</view>
		</view>

		<view class="bottom-bar" v-if="storeInfo">
			<view class="icon-group">
				<view class="icon-item" @click="handleFavorite">
					<u-icon :name="isFavorited ? 'star-fill' : 'star'" :color="isFavorited ? '#ff6b6b' : '#666'" size="22"></u-icon>
					<text :style="{ color: isFavorited ? '#ff6b6b' : '#666' }">{{ isFavorited ? '已收藏' : '收藏' }}</text>
				</view>
				<view class="icon-item" @click="handleShare">
					<u-icon name="share" size="22"></u-icon>
					<text>分享</text>
				</view>
			</view>
			<view class="button-group">
				<view class="action-btn wechat-btn" @click="contactViaWechat">
					<u-icon name="weixin-fill" color="#fff" size="20"></u-icon>
					<text>微信</text>
				</view>
				<view class="action-btn call-btn" @click="callPhone(phoneNumbers[0])">
					<u-icon name="phone-fill" color="#fff" size="20"></u-icon>
					<text>电话</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { addMerchantTransfer } from "@/api/buy/merchantTransfer.js"
	import { getStoreInfo } from "@/api/buy/storeInfo.js"
	import { listProduct } from "@/api/buy/product.js"
	import { addSeeLog, listSeeLog } from "@/api/buy/seeLog.js"
	import { listFavorite, addFavorite, delFavorite } from "@/api/buy/favorite.js"
	import { baseUrl } from "../../config";
	import eventBus from "@/utils/eventBus";
	
	export default {
		data() {
			return {
				storeId: '',
				storeInfo: null,
				productList: [], // 商品列表
				baseUrl,
				isMainBusinessExpanded: false, // 主营业务是否展开
				isFavorited: false, // 是否已收藏
				favoriteId: null, // 收藏记录ID
				visitorList: [], // 访客记录列表
				loadingVisitors: false, // 访客记录加载状态
				// 单选题相关数据
				selectedOptionId: null, // 选中的选项ID
				showResult: false, // 是否显示结果
				isAnswerCorrect: false, // 答案是否正确
			}
		},
		onLoad(options) {
			if (options.id) {
				this.storeId = options.id;
				this.getStoreDetails();
				// 记录店铺访问记录
				this.recordVisit();
				// 检查收藏状态
				this.checkFavoriteStatus();
			} else {
				this.$modal.msgError("缺少店铺ID");
			}
		},
		computed: {
			_splitUrls() {
				return (urls) => {
					if (!urls || typeof urls !== 'string') return [];
					return urls.split(',').filter(url => url.trim()).map(url => this.getFullImageUrl(url));
				}
			},
			mainImages() {
				if (!this.storeInfo || !this.storeInfo.avatar) return [];
				return [{ url: this.getFullImageUrl(this.storeInfo.avatar) }];
			},
			displayImages() {
				return this.storeInfo ? this._splitUrls(this.storeInfo.displayImages) : [];
			},
			displayImagesLimited() {
				return this.displayImages.slice(0, 6);
			},
			factoryImages() {
				return this.storeInfo ? this._splitUrls(this.storeInfo.factoryImages) : [];
			},
			factoryImagesLimited() {
				return this.factoryImages.slice(0, 6);
			},
			videoList() {
				return this.storeInfo ? this._splitUrls(this.storeInfo.videoUrl) : [];
			},
			displayProducts() {
				return this.productList.slice(0, 6);
			},
			qualificationImages() {
				return this.storeInfo ? this._splitUrls(this.storeInfo.qualificationImages) : [];
			},
			firstQualificationImage() {
				return this.qualificationImages.length > 0 ? this.qualificationImages[0] : null;
			},
			phoneNumbers() {
				if (!this.storeInfo || !this.storeInfo.phone) return [];
				return this.storeInfo.phone.split(',').filter(phone => phone.trim());
			},
			// 判断是否需要显示展开按钮（超过100个字符）
			shouldShowExpandButton() {
				return this.storeInfo && this.storeInfo.mainBusiness && this.storeInfo.mainBusiness.length > 100;
			},
			// 将访客列表按每行2个进行分组
			visitorRows() {
				const rows = [];
				for (let i = 0; i < this.visitorList.length; i += 2) {
					rows.push(this.visitorList.slice(i, i + 2));
				}
				return rows;
			}
		},
		methods: {
			// 获取完整的图片URL
			getFullImageUrl(url) {
				if (!url) return '';
				// 如果已经是完整的URL，直接返回
				if (url.startsWith('http://') || url.startsWith('https://')) {
					return url;
				}
				// 否则拼接baseUrl
				return this.baseUrl + url;
			},

			getStoreDetails() {
				this.$modal.loading("加载中...");
				getStoreInfo(this.storeId).then(res => {
					if (res.code === 200 && res.data) {
						this.storeInfo = res.data;
						uni.setNavigationBarTitle({ title: this.storeInfo.storeName || '店铺详情' });
						// 获取店铺商品
						this.getStoreProducts();
						// 如果店铺开启了访客信息展示，则获取访客记录
						if (this.storeInfo.showVisitorInfo === '1' || this.storeInfo.showVisitorInfo === 1) {
							this.getVisitorList();
						}
					} else {
						this.$modal.closeLoading();
						this.$modal.msgError(res.msg || '店铺信息不存在');
					}
				}).catch(error => {
					this.$modal.closeLoading();
					console.error('获取店铺详情失败:', error);
					this.$modal.msgError("加载失败，请稍后重试");
				});
			},

			// 获取店铺商品
			getStoreProducts() {
				listProduct({
					storeId: this.storeId,
					pageNum: 1,
					pageSize: 10
				}).then(res => {
					this.$modal.closeLoading();
					if (res.code === 200) {
						this.productList = res.rows || [];
					}
				}).catch(error => {
					this.$modal.closeLoading();
					console.error('获取商品列表失败:', error);
				});
			},
			
			previewSwiper(index) {
				this.previewImage(this.mainImages[index].url, [this.mainImages[index].url])
			},
			
			previewImage(current, urls) {
				uni.previewImage({ current, urls });
			},
			
			callPhone(phone) {
				if(!phone) {
					this.$modal.msg("暂无电话信息");
					return;
				}
				uni.makePhoneCall({ phoneNumber: phone });
			},
			
			openMap() {
				const { latitude, longitude, address } = this.storeInfo;
				if(!latitude || !longitude) {
					this.$modal.msg("暂无地址信息");
					return;
				}
				uni.openLocation({ latitude, longitude, address });
			},
			
			async handleFavorite() {
				// 检查用户是否登录
				if (!this.$store.state.user || !this.$store.state.user.userId) {
					this.$modal.msg('请先登录');
					return;
				}

				try {
					if (this.isFavorited) {
						// 取消收藏
						await this.removeFavorite();
					} else {
						// 添加收藏
						await this.addToFavorite();
					}
				} catch (error) {
					console.error('收藏操作失败:', error);
					this.$modal.msgError('操作失败，请稍后重试');
				}
			},
			
			handleShare() {
				this.$modal.msg('分享功能待实现');
			},

			// 检查收藏状态
			async checkFavoriteStatus() {
				if (!this.$store.state.user || !this.$store.state.user.userId) {
					return;
				}

				try {
					const response = await listFavorite({
						userId: this.$store.state.user.userId,
						targetId: this.storeId,
						targetType: 'store'
					});

					if (response.code === 200 && response.rows && response.rows.length > 0) {
						this.isFavorited = true;
						this.favoriteId = response.rows[0].favoriteId;
					} else {
						this.isFavorited = false;
						this.favoriteId = null;
					}
				} catch (error) {
					console.error('检查收藏状态失败:', error);
				}
			},

			// 添加收藏
			async addToFavorite() {
				const response = await addFavorite({
					userId: this.$store.state.user.userId,
					targetId: this.storeId,
					targetType: 'store'
				});

				if (response.code === 200) {
					this.isFavorited = true;
					this.favoriteId = response.data?.favoriteId;
					this.$modal.msgSuccess('收藏成功');

					// 触发收藏变化事件
					eventBus.emit('favoriteChanged', {
						action: 'add',
						targetType: 'store',
						targetId: this.storeId
					});
				} else {
					throw new Error(response.msg || '收藏失败');
				}
			},

			// 取消收藏
			async removeFavorite() {
				if (!this.favoriteId) {
					// 如果没有favoriteId，重新查询获取
					await this.checkFavoriteStatus();
					if (!this.favoriteId) {
						throw new Error('未找到收藏记录');
					}
				}

				const response = await delFavorite(this.favoriteId);

				if (response.code === 200) {
					this.isFavorited = false;
					this.favoriteId = null;
					this.$modal.msgSuccess('取消收藏成功');

					// 触发收藏变化事件
					eventBus.emit('favoriteChanged', {
						action: 'remove',
						targetType: 'store',
						targetId: this.storeId
					});
				} else {
					throw new Error(response.msg || '取消收藏失败');
				}
			},
			
			contactViaWechat() {
				if(!this.storeInfo.wechatQrcode){
					this.$modal.msg("暂无微信二维码");
					return;
				}
				this.previewImage(this.storeInfo.wechatQrcode, [this.storeInfo.wechatQrcode]);
			},
			
			contactSeller() {
				this.$modal.msg('在线联系功能待实现');
			},

			// 获取商品第一张图片
			getProductImage(product) {
				if (!product.productImages) return '/static/images/default-product.png';
				const images = product.productImages.split(',');
				return this.getFullImageUrl(images[0]);
			},

			// 跳转到商品详情
			goToProductDetail(product) {
				uni.navigateTo({
					url: `/pages/productDetail/productDetail?id=${product.productId}`
				});
			},

			// 跳转到商品列表
			goToProductList() {
				uni.navigateTo({
					url: `/pages/productList/productList?storeId=${this.storeId}`
				});
			},

			// 跳转到图片画廊
			goToImageGallery(type) {
				const images = type === 'display' ? this.displayImages : this.factoryImages;
				uni.navigateTo({
					url: `/pages/imageGallery/imageGallery?images=${encodeURIComponent(JSON.stringify(images))}&title=${type === 'display' ? '店铺展示' : '库房工厂'}`
				});
			},

			// 跳转到视频画廊
			goToVideoGallery() {
				uni.navigateTo({
					url: `/pages/videoGallery/videoGallery?videos=${encodeURIComponent(JSON.stringify(this.videoList))}&title=店铺视频`
				});
			},

			// 切换主营业务展开/收缩
			toggleMainBusiness() {
				this.isMainBusinessExpanded = !this.isMainBusinessExpanded;
			},

			// 记录访问记录
			recordVisit() {
				try {
					// 获取用户ID，如果未登录则为0
					let userId = 0;
					if (this.$store.state.user && this.$store.state.user.userId) {
						userId = this.$store.state.user.userId;
					}

					const visitData = {
						userId: userId,
						targetId: this.storeId,
						type: '0' // 店铺访问记录
					};

					// 异步记录访问，不影响页面加载
					addSeeLog(visitData).then(res => {
						console.log('店铺访问记录已记录:', res);
					}).catch(error => {
						console.error('记录店铺访问失败:', error);
					});
				} catch (error) {
					console.error('记录访问记录异常:', error);
				}
			},

			// 获取访客记录列表
			getVisitorList() {
				this.loadingVisitors = true;
				const params = {
					type: '0', // 店铺访问记录
					targetId: this.storeId,
					pageNum: 1,
					pageSize: 20 // 只显示20条记录
				};

				listSeeLog(params).then(res => {
					this.loadingVisitors = false;
					if (res.code === 200) {
						this.visitorList = res.rows || [];
					} else {
						console.error('获取访客记录失败:', res.msg);
						this.visitorList = [];
					}
				}).catch(error => {
					this.loadingVisitors = false;
					console.error('获取访客记录失败:', error);
					this.visitorList = [];
				});
			},

			// 格式化访问时间
			formatVisitTime(timeStr) {
				if (!timeStr) return '';

				const visitTime = new Date(timeStr);
				const now = new Date();
				const diff = now - visitTime;

				// 计算时间差
				const minutes = Math.floor(diff / (1000 * 60));
				const hours = Math.floor(diff / (1000 * 60 * 60));
				const days = Math.floor(diff / (1000 * 60 * 60 * 24));

				if (minutes < 1) {
					return '刚刚';
				} else if (minutes < 60) {
					return `${minutes}分钟前`;
				} else if (hours < 24) {
					return `${hours}小时前`;
				} else if (days < 7) {
					return `${days}天前`;
				} else {
					// 超过7天显示具体日期
					const month = visitTime.getMonth() + 1;
					const date = visitTime.getDate();
					return `${month}月${date}日`;
				}
			},

			// 单选题相关方法
			// 选择选项
			selectOption(optionId) {
				if (this.showResult) return; // 已提交答案后不能再选择
				this.selectedOptionId = optionId;
			},

			// 提交答案
			async submitAnswer() {
				if (!this.selectedOptionId) return;

				// 检查答案是否正确
				this.isAnswerCorrect = this.storeInfo.question.correctAnswerIds.includes(this.selectedOptionId);

				// 显示结果提示
				if (this.isAnswerCorrect) {
					this.showResult = true;
					uni.showToast({
						title: '回答正确！',
						icon: 'success'
					});

					// 回答正确后调用商户转移API
					await this.callMerchantTransfer();
				} else {
					// 答错时显示错误提示并重置状态
					uni.showToast({
						title: '回答错误，请重新选择',
						icon: 'none'
					});
					// 延迟重置状态，让用户看到提示
					setTimeout(() => {
						this.selectedOptionId = null;
						this.showResult = false;
						this.isAnswerCorrect = false;
					}, 1500);
				}
			},

			// 调用商户转移API
			async callMerchantTransfer() {
				try {
					// 获取微信登录code
					const loginRes = await this.getWxLoginCode();
					if (!loginRes.code) {
						console.error('获取微信授权失败');
						return;
					}

					// 调用商户转移API
					const transferData = {
						code: loginRes.code,
						storeId: this.storeId
					};

					const res = await addMerchantTransfer(transferData);
					if (res.code === 200) {
						console.log('商户转移调用成功:', res);

						// 使用返回的packageInfo调起微信确认转账页面
						if (res.data && res.data.packageInfo) {
							await this.requestMerchantTransfer(res.data.packageInfo);
						} else {
							console.error('返回数据中没有packageInfo字段');
						}
					} else {
						console.error('商户转移调用失败:', res.msg);
					}
				} catch (error) {
					console.error('商户转移API调用异常:', error);
				}
			},

			// 调起微信确认转账页面
			async requestMerchantTransfer(packageInfo) {
				try {
					console.log('调起微信确认转账页面，packageInfo:', packageInfo);

					const transferRes = await new Promise((resolve, reject) => {
						wx.requestMerchantTransfer({
							package: packageInfo,
							appId: "wx4d16a89e820ad69d",
							mchId:"1722468463",
							success: (res) => {
								console.log('微信确认转账成功:', res);
								resolve(res);
							},
							fail: (err) => {
								console.error('微信确认转账失败:', err);
								reject(err);
							}
						});
					});

					// 转账成功处理
					uni.showToast({
						title: '转账成功！',
						icon: 'success'
					});

				} catch (error) {
					console.error('微信确认转账异常:', error);

					// 根据错误类型显示不同提示
					if (error.errMsg && error.errMsg.includes('cancel')) {
						uni.showToast({
							title: '转账已取消',
							icon: 'none'
						});
					} else {
						uni.showToast({
							title: '转账失败',
							icon: 'none'
						});
					}
				}
			},

			// 获取微信登录code
			getWxLoginCode() {
				return new Promise((resolve, reject) => {
					uni.login({
						provider: 'weixin',
						success: (res) => {
							resolve(res);
						},
						fail: (err) => {
							reject(err);
						}
					});
				});
			},

			// 判断选项是否为正确答案
			isCorrectOption(optionId) {
				if (!this.storeInfo.question || !this.storeInfo.question.correctAnswerIds) {
					return false;
				}
				return this.storeInfo.question.correctAnswerIds.includes(optionId);
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f5f5f5;
		padding-bottom: 120rpx; 
	}
	.content-wrapper {
		padding: 20rpx;
	}

	.card-section {
		background-color: #fff;
		padding: 24rpx;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
	}
	
	.product-header {
		margin-top: -80rpx;
		position: relative;
		z-index: 2;
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #303133;
			display: block;
		}
		.price-line {
			margin-top: 16rpx;
			.price {
				font-size: 38rpx;
				font-weight: bold;
				color: #fa3534;
			}
		}
		.info-line {
			margin-top: 16rpx;
			font-size: 26rpx;
			color: #606266;
			word-break: break-all;

			.main-business-container {
				flex: 1;

				.main-business-text {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					overflow: hidden;
					-webkit-line-clamp: 3; // 默认显示3行
					line-height: 1.5;

					&.expanded {
						-webkit-line-clamp: unset;
						display: block;
					}
				}

				.expand-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-top: 8rpx;
					padding: 8rpx 16rpx;
					background-color: #f8f9fa;
					border-radius: 20rpx;

					.expand-text {
						font-size: 24rpx;
						color: #999;
						margin-right: 4rpx;
					}
				}
			}
		}
	}
	
	.section-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;
		.title-text {
			font-size: 32rpx;
			font-weight: bold;
			color: #303133;
			margin-left: 10rpx;
			flex: 1;
		}
		.more-btn {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #999;

			text {
				margin-right: 4rpx;
			}
		}
	}
	
	.grid-image {
		width: 100%;
		height: 200rpx;
		border-radius: 8rpx;
	}

	.products-section {
		.product-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 8rpx;

			.product-image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 12rpx;
				margin-bottom: 12rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
			}

			.product-name {
				font-size: 26rpx;
				color: #303133;
				text-align: center;
				line-height: 1.4;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				width: 100%;
				padding: 0 8rpx;
			}
		}
	}
	
	.video-player {
		width: 100%;
		border-radius: 8rpx;
	}
	
	.contact-section {
		.contact-item {
			font-size: 28rpx;
			color: #303133;
			padding: 16rpx 0;
			display: flex;
			line-height: 1.6;
			
			// 【修复点】给带按钮的行加上align-items
			&.with-button {
				align-items: center;
			}

			.item-label {
				color: #606266;
				white-space: nowrap;
				.placeholder {
					color: transparent;
				}
			}
			.item-content {
				word-break: break-all;
			}
			
			// 【修复点】新增的文本包裹容器样式
			.item-text-wrapper {
				flex: 1; // 占据所有可用空间
				min-width: 0; // 允许在 flex 布局中被压缩
				margin-right: 20rpx; // 和按钮之间留出间距
				display: flex;
				align-items: center; // 垂直居中对齐

				.item-label {
					flex-shrink: 0; // 标签不收缩
					margin-right: 16rpx; // 标签和内容之间的间距
					font-size: 28rpx;
					color: #666;

					.placeholder {
						opacity: 0;
					}
				}

				.item-content {
					flex: 1;
					min-width: 0; // 允许收缩
					font-size: 28rpx;
					color: #333;
					word-break: break-all;
					line-height: 1.4;
				}
			}

			// 自定义按钮样式
			.contact-btn {
				padding: 8rpx 20rpx;
				border-radius: 20rpx;
				border: none;
				font-size: 24rpx;
				color: #fff;
				min-width: 80rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-shrink: 0; // 按钮不收缩

				&.call-btn {
					background: linear-gradient(45deg, #ff6b6b, #ff8e8e);

					&:active {
						background: linear-gradient(45deg, #ff5252, #ff7979);
					}
				}

				&.nav-btn {
					background: linear-gradient(45deg, #2196f3, #64b5f6);

					&:active {
						background: linear-gradient(45deg, #1976d2, #42a5f5);
					}
				}
			}
		}
		.contact-qr-cards {
			display: flex;
			justify-content: space-around;
			margin-top: 20rpx;
			padding-top: 20rpx;
			border-top: 1px solid #f0f0f0;
			.qr-card {
				display: flex;
				flex-direction: column;
				align-items: center;
				.qr-image {
					width: 200rpx;
					height: 200rpx;
					margin-bottom: 10rpx;
					border: 1px solid #eee;
				}
				.qr-text {
					font-size: 24rpx;
					color: #606266;
				}
			}
		}
	}
	
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80vh;
	}
	
	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 110rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		box-sizing: content-box;
		
		.icon-group {
			display: flex;
			align-items: center;
			.icon-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				font-size: 22rpx;
				color: #606266;
				margin-right: 40rpx;
			}
		}
		
		.button-group {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			
			.action-btn {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 5rpx !important;
				height: 80rpx;
				font-size: 28rpx;
				font-weight: 600;
				border-radius: 40rpx;
				transition: all 0.3s ease;

				text {
					margin-left: 8rpx;
					font-size: 28rpx;
					font-weight: 600;
				}
			}
			
			.wechat-btn {
				background: linear-gradient(45deg, #07c160, #19be6b) !important;
				color: #fff !important;
				border: none !important;
				box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);

				&:active {
					background: linear-gradient(45deg, #06ad56, #17a85e) !important;
					transform: scale(0.98);
				}
			}

			.call-btn {
				background: linear-gradient(45deg, #fa3534, #ff6b6b) !important;
				color: #fff !important;
				border: none !important;
				box-shadow: 0 4rpx 12rpx rgba(250, 53, 52, 0.3);

				&:active {
					background: linear-gradient(45deg, #e12e2d, #ff5252) !important;
					transform: scale(0.98);
				}
			}
		}
	}

	// 访客记录样式
	.visitor-section {
		.visitor-list {
			margin-top: 20rpx;
		}

		.visitor-row {
			display: flex;
			justify-content: space-between;
			margin-bottom: 24rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.visitor-item {
			display: flex;
			align-items: center;
			flex: 1;
			max-width: 48%;

			.visitor-avatar {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				overflow: hidden;
				margin-right: 16rpx;
				flex-shrink: 0;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.visitor-info {
				flex: 1;
				min-width: 0;

				.visitor-name {
					display: block;
					font-size: 28rpx;
					color: #303133;
					font-weight: 500;
					margin-bottom: 4rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.visitor-time {
					display: block;
					font-size: 24rpx;
					color: #909399;
				}
			}
		}

		.visitor-empty {
			margin-top: 20rpx;
		}

		.visitor-loading {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 40rpx 0;

			text {
				margin-top: 16rpx;
				font-size: 26rpx;
				color: #909399;
			}
		}
	}

	// 单选题样式
	.question-section {
		margin-bottom: 20rpx;

		.question-content {
			margin-top: 20rpx;

			.question-text {
				font-size: 32rpx;
				font-weight: 600;
				color: #303133;
				line-height: 1.5;
				margin-bottom: 24rpx;
				display: block;
			}

			.options-list {
				.option-item {
					display: flex;
					align-items: center;
					padding: 20rpx 16rpx;
					margin-bottom: 16rpx;
					background: #f8f9fa;
					border-radius: 12rpx;
					border: 2rpx solid transparent;
					transition: all 0.3s ease;
					position: relative;

					&.selected {
						background: rgba(250, 53, 52, 0.05);
						border-color: #fa3534;
					}

					.option-radio {
						width: 40rpx;
						height: 40rpx;
						border: 2rpx solid #ddd;
						border-radius: 50%;
						margin-right: 16rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						flex-shrink: 0;

						.radio-inner {
							width: 20rpx;
							height: 20rpx;
							background: #fa3534;
							border-radius: 50%;
						}
					}

					&.selected .option-radio {
						border-color: #fa3534;
					}

					.option-text {
						flex: 1;
						font-size: 30rpx;
						color: #303133;
						line-height: 1.4;
					}
				}
			}

			.question-actions {
				margin-top: 30rpx;
				display: flex;
				justify-content: center;

				.submit-btn {
					background: linear-gradient(135deg, #fa3534 0%, #ff6b6b 100%);
					color: #fff;
					border: none;
					border-radius: 50rpx;
					padding: 24rpx 60rpx;
					font-size: 32rpx;
					font-weight: 600;
					box-shadow: 0 8rpx 20rpx rgba(250, 53, 52, 0.3);
					transition: all 0.3s ease;

					&:active {
						transform: translateY(2rpx);
						box-shadow: 0 4rpx 12rpx rgba(250, 53, 52, 0.4);
					}

					&[disabled] {
						background: #c8c9cc;
						box-shadow: none;
						transform: none;
					}

					.submit-text {
						color: #fff;
					}
				}
			}

			.result-message {
				margin-top: 24rpx;
				text-align: center;

				.result-text {
					font-size: 30rpx;
					font-weight: 600;
					padding: 16rpx 24rpx;
					border-radius: 8rpx;

					&.correct {
						color: #19be6b;
						background: rgba(25, 190, 107, 0.1);
					}
				}
			}
		}
	}
</style>