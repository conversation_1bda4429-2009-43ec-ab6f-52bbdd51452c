<template>
	<view class="buy-group-container">
		<view class="header-section">
			<view class="title">
				<text class="main-title">买货群</text>
				<text class="sub-title">扫码加入，获取更多优质货源信息</text>
			</view>
		</view>

		<view class="qr-section">
			<view class="qr-card">
				<view class="qr-header">
					<text class="qr-title">微信扫码加入买货群</text>
					<text class="qr-desc">与更多商友交流，获取第一手货源信息</text>
				</view>
				
				<view class="qr-code-wrapper">
					<image
						class="qr-code"
						src="/static/images/buy-group-qr.png"
						mode="aspectFit"
						@error="onImageError"
					></image>
					<!-- 临时占位提示 -->
					<view class="qr-placeholder" v-if="showPlaceholder">
						<uni-icons type="image" color="#ccc" size="80"></uni-icons>
						<text class="placeholder-text">请添加买货群二维码图片</text>
						<text class="placeholder-desc">图片路径：/static/images/buy-group-qr.png</text>
					</view>
				</view>

				<view class="qr-footer">
					<view class="tips">
						<view class="tip-item">
							<uni-icons type="checkmarkempty" color="#19be6b" size="16"></uni-icons>
							<text class="tip-text">实时货源信息推送</text>
						</view>
						<view class="tip-item">
							<uni-icons type="checkmarkempty" color="#19be6b" size="16"></uni-icons>
							<text class="tip-text">专业采购经验分享</text>
						</view>
						<view class="tip-item">
							<uni-icons type="checkmarkempty" color="#19be6b" size="16"></uni-icons>
							<text class="tip-text">优质供应商推荐</text>
						</view>
					</view>
				</view>
			</view>
		</view>


		<!-- 自定义底部导航栏 -->
		<custom-tabbar ref="customTabbar" :current="0" @change="onTabChange"></custom-tabbar>
	</view>
</template>

<script>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'

export default {
	components: {
		CustomTabbar
	},
	data() {
		return {
			showPlaceholder: false
		}
	},
	
	onLoad() {
		uni.setNavigationBarTitle({
			title: '买货群'
		});
	},

	onShow() {
		// 同步tabBar状态
		this.$nextTick(() => {
			if (this.$refs.customTabbar) {
				this.$refs.customTabbar.syncCurrentPageState();
			}
		});
	},
	
	methods: {
		// 图片加载失败处理
		onImageError() {
			this.showPlaceholder = true;
			console.log('二维码图片加载失败，显示占位符');
		},

		// tabBar切换事件
		onTabChange(e) {
			console.log('Tab切换到:', e.index, e.pagePath);
		}
	}
}
</script>

<style lang="scss">
page {
	height: 100%;
	background: linear-gradient(to bottom, #F5C6CB, #ffffff 50%);
}

.buy-group-container {
	min-height: 100vh;
	background-color: transparent;
	padding: 40rpx 30rpx;
	padding-bottom: 160rpx; /* 为自定义tabBar预留空间 */
}

.header-section {
	text-align: center;
	margin-bottom: 60rpx;

	.title {
		.main-title {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #DD1A21;
			text-shadow: 0 2rpx 4rpx rgba(221, 26, 33, 0.3);
			margin-bottom: 16rpx;
		}

		.sub-title {
			display: block;
			font-size: 28rpx;
			color: #666;
			font-weight: 500;
		}
	}
}

.qr-section {
	margin-bottom: 40rpx;

	.qr-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);

		.qr-header {
			text-align: center;
			margin-bottom: 40rpx;

			.qr-title {
				display: block;
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 12rpx;
			}

			.qr-desc {
				display: block;
				font-size: 26rpx;
				color: #666;
			}
		}

		.qr-code-wrapper {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-bottom: 40rpx;
			position: relative;

			.qr-code {
				width: 400rpx;
				height: 400rpx;
				border-radius: 16rpx;
				border: 1px solid #f0f0f0;
			}

			.qr-placeholder {
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 400rpx;
				height: 400rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background-color: #f8f9fa;
				border: 2px dashed #ddd;
				border-radius: 16rpx;

				.placeholder-text {
					margin-top: 20rpx;
					font-size: 26rpx;
					color: #666;
				}

				.placeholder-desc {
					margin-top: 8rpx;
					font-size: 22rpx;
					color: #999;
					text-align: center;
					padding: 0 20rpx;
				}
			}
		}

		.qr-footer {
			.tips {
				.tip-item {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.tip-text {
						margin-left: 12rpx;
						font-size: 26rpx;
						color: #666;
					}
				}
			}
		}
	}
}


</style>
