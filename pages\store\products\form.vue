<template>
	<view class="page-container">
		<u-form :model="form" ref="uForm" label-position="top" label-width="auto">
			<!-- 基本信息 -->
			<view class="form-block">
				<view class="block-title">基本信息</view>
				
				<u-form-item label="商品名称" prop="productName" required>
					<u-input 
						v-model="form.productName" 
						placeholder="请输入商品名称" 
						border="bottom"
						:maxlength="100"
					/>
				</u-form-item>
				
				<u-form-item label="商品分类" prop="categoryId" required>
					<view class="category-selector" @click="showCategoryPicker">
						<text class="category-text" :class="{'placeholder': !selectedCategory}">
							{{ selectedCategory ? selectedCategory.categoryName : '请选择商品分类' }}
						</text>
						<u-icon name="arrow-right" size="16" color="#C8C7CC"></u-icon>
					</view>
				</u-form-item>
				
				<u-form-item label="是否推荐">
					<u-switch 
						v-model="form.isRecommended" 
						:active-value="'1'" 
						:inactive-value="'0'"
						active-color="#2979ff"
					></u-switch>
				</u-form-item>
			</view>

			<!-- 商品图片 -->
			<view class="form-block">
				<view class="block-title">商品图片</view>
				<u-form-item label="商品图片" prop="productImages">
					<u-upload
						:file-list="imageList"
						@afterRead="afterRead"
						@delete="deletePic"
						name="productImages"
						multiple
						:max-count="9"
						:action="uploadUrl"
						:header="header"
					></u-upload>
					<view class="upload-tip">
						<text>最多可上传9张图片，建议尺寸750x750像素</text>
					</view>
				</u-form-item>
			</view>


		</u-form>
		
		<!-- 底部操作按钮 -->
		<view class="button-wrapper">
			<u-button type="primary" :text="isEdit ? '保存修改' : '添加商品'" @click="submitForm"></u-button>
		</view>

		<!-- 分类选择器 -->
		<u-picker 
			:show="showCategorySelector" 
			:columns="[categoryOptions]" 
			@confirm="onCategoryConfirm" 
			@cancel="onCategoryCancel"
			:default-index="[selectedCategoryIndex]"
		></u-picker>
	</view>
</template>

<script>
	import { getProduct, addProduct, updateProduct } from "@/api/buy/product.js";
	import { listProductCategory } from "@/api/buy/productCategory.js";
	import { listStoreInfo } from "@/api/buy/storeInfo.js";
	import { getToken } from "@/utils/auth";
	import { baseUrl } from "@/config.js";

	export default {
		data() {
			return {
				userId: this.$store.state.user.userId,
				storeId: null,
				productId: null,
				isEdit: false,
				baseUrl,
				header: { Authorization: 'Bearer ' + getToken() },
				uploadUrl: '',
				
				form: {
					productId: null,
					storeId: null,
					categoryId: null,
					productName: '',
					productImages: '',
					isRecommended: '0'
				},
				
				imageList: [],
				categoryList: [],
				categoryOptions: [],
				selectedCategory: null,
				selectedCategoryIndex: 0,
				showCategorySelector: false,
				
				rules: {
					'productName': { 
						type: 'string', 
						required: true, 
						message: '请输入商品名称', 
						trigger: ['blur', 'change'] 
					},
					'categoryId': { 
						type: 'number', 
						required: true, 
						message: '请选择商品分类', 
						trigger: ['blur', 'change'] 
					}
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.productId = options.id;
				this.isEdit = true;
				uni.setNavigationBarTitle({ title: '编辑商品' });
			} else {
				uni.setNavigationBarTitle({ title: '新增商品' });
			}
			this.initUploadUrl();
			this.getStoreInfo();
		},
		onShow() {
			// 从分类管理页面返回时重新获取分类数据
			if (this.storeId) {
				this.getCategoryList();
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			// 初始化上传URL
			initUploadUrl() {
				const baseUrl = process.env.VUE_APP_BASE_API;
				if (baseUrl && baseUrl !== 'undefined' && baseUrl.trim() !== '') {
					this.uploadUrl = baseUrl + '/common/upload';
				} else {
					try {
						const config = require('@/config.js');
						if (config && config.baseUrl) {
							this.uploadUrl = config.baseUrl + '/common/upload';
						} else {
							this.uploadUrl = 'http://localhost:8080/common/upload';
						}
					} catch (error) {
						this.uploadUrl = 'http://localhost:8080/common/upload';
					}
				}
			},

			// 获取店铺信息
			async getStoreInfo() {
				try {
					this.$modal.loading('加载中...');
					const res = await listStoreInfo({ userId: this.userId, pageNum: 1, pageSize: 1 });
					
					if (res.code === 200 && res.rows.length > 0) {
						this.storeId = res.rows[0].storeId;
						this.form.storeId = this.storeId;
						await this.getCategoryList();
						if (this.isEdit) {
							await this.getProductDetail();
						}
					} else {
						this.$modal.msgError('请先完成店铺入驻');
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
					this.$modal.closeLoading();
				} catch (error) {
					this.$modal.closeLoading();
					this.$modal.msgError('获取店铺信息失败');
				}
			},

			// 获取分类列表
			async getCategoryList() {
				try {
					const res = await listProductCategory({ 
						storeId: this.storeId, 
						pageSize: 1000 
					});
					if (res.code === 200) {
						this.categoryList = res.rows || [];
						this.categoryOptions = this.categoryList.map(item => item.categoryName);
						
						if (this.categoryList.length === 0) {
							this.$modal.msgError('请先添加商品分类');
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/store/category/index'
								});
							}, 1500);
						}
					}
				} catch (error) {
					console.error('获取分类列表失败:', error);
				}
			},

			// 获取商品详情
			async getProductDetail() {
				try {
					const res = await getProduct(this.productId);
					if (res.code === 200) {
						this.form = { ...res.data };
						this.initImageList();
						this.initSelectedCategory();
					} else {
						this.$modal.msgError(res.msg || '获取商品详情失败');
					}
				} catch (error) {
					this.$modal.msgError('获取商品详情失败');
				}
			},

			// 初始化图片列表
			initImageList() {
				if (this.form.productImages) {
					this.imageList = this.form.productImages.split(',').map(url => {
						// 如果不是完整URL，拼接baseUrl
						if (url && !url.startsWith('http://') && !url.startsWith('https://')) {
							return { url: this.baseUrl + url };
						}
						return { url };
					});
				}
			},

			// 初始化选中的分类
			initSelectedCategory() {
				if (this.form.categoryId) {
					const category = this.categoryList.find(item => item.categoryId === this.form.categoryId);
					if (category) {
						this.selectedCategory = category;
						this.selectedCategoryIndex = this.categoryList.findIndex(item => item.categoryId === this.form.categoryId);
					}
				}
			},

			// 显示分类选择器
			showCategoryPicker() {
				if (this.categoryList.length === 0) {
					this.$modal.msgError('暂无可选分类，请先添加分类');
					return;
				}
				this.showCategorySelector = true;
			},

			// 分类选择确认
			onCategoryConfirm(e) {
				const { indexs } = e;
				this.selectedCategoryIndex = indexs[0];
				this.selectedCategory = this.categoryList[indexs[0]];
				this.form.categoryId = this.selectedCategory.categoryId;
				this.showCategorySelector = false;
			},

			// 分类选择取消
			onCategoryCancel() {
				this.showCategorySelector = false;
			},

			// 图片上传成功
			async afterRead(event) {
				console.log('afterRead event:', event);
				let fileList = this.imageList;
				
				let files = [];
				if (Array.isArray(event.file)) {
					files = event.file;
				} else if (event.file) {
					files = [event.file];
				} else {
					return;
				}
				
				files.forEach(item => {
					fileList.push({ ...item, status: 'uploading', message: '上传中' });
				});
				
				for (let i = 0; i < fileList.length; i++) {
					const item = fileList[i];
					if (item.status !== 'uploading') continue;
					
					try {
						const result = await uni.uploadFile({
							url: this.uploadUrl,
							filePath: item.url,
							name: 'file',
							header: this.header
						});
						
						let uploadResult = result;
						if (Array.isArray(result)) {
							uploadResult = result[1] || result[0];
						}
						
						if (!uploadResult || !uploadResult.data) {
							item.status = 'failed';
							item.message = '服务器未返回数据';
							continue;
						}
						
						let responseData;
						try {
							responseData = JSON.parse(uploadResult.data);
						} catch (parseError) {
							item.status = 'failed';
							item.message = 'JSON解析失败';
							continue;
						}
						
						if (responseData.code === 200) {
							item.status = 'success';
							item.message = '';
							if (responseData.fileName) {
								item.url = this.baseUrl + responseData.fileName;
							} else if (responseData.url) {
								item.url = responseData.url;
							}
						} else {
							item.status = 'failed';
							item.message = responseData.msg || '上传失败';
						}
					} catch (e) {
						item.status = 'failed';
						item.message = '上传异常';
					}
				}
				this.updateFormImages();
			},

			// 删除图片
			deletePic(event) {
				this.imageList.splice(event.index, 1);
				this.updateFormImages();
			},

			// 更新表单图片字段
			updateFormImages() {
				this.form.productImages = this.imageList.map(file => {
					// 如果URL包含baseUrl，则去掉baseUrl只保存相对路径
					if (file.url && file.url.startsWith(this.baseUrl)) {
						return file.url.replace(this.baseUrl, '');
					}
					return file.url;
				}).join(',');
			},

			// 提交表单
			submitForm() {
				this.$refs.uForm.validate().then(res => {
					this.$modal.loading(this.isEdit ? '保存中...' : '添加中...');
					const api = this.isEdit ? updateProduct : addProduct;
					const successMsg = this.isEdit ? '修改成功' : '添加成功';

					api(this.form).then(response => {
						this.$modal.closeLoading();
						if (response.code === 200) {
							this.$modal.msgSuccess(successMsg);
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						} else {
							this.$modal.msgError(response.msg || '操作失败');
						}
					}).catch(() => {
						this.$modal.closeLoading();
						this.$modal.msgError('操作失败');
					});
				}).catch(errors => {
					console.log('表单验证失败:', errors);
				});
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 10px 10px 80px 10px;
	}

	.form-block {
		background-color: #ffffff;
		border-radius: 8px;
		padding: 5px 15px;
		margin-bottom: 12px;
		
		.block-title {
			font-size: 16px;
			font-weight: 600;
			color: #303133;
			padding: 15px 0 10px 0;
			border-bottom: 1px solid #f0f0f0;
			margin-bottom: 10px;
		}
	}

	.category-selector {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1px solid #DADBDE;
		min-height: 44rpx;
		
		.category-text {
			flex: 1;
			font-size: 28rpx;
			color: #303133;
			
			&.placeholder {
				color: #C0C4CC;
			}
		}
	}

	.upload-tip {
		margin-top: 8px;
		
		text {
			font-size: 12px;
			color: #909399;
		}
	}

	.button-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #ffffff;
		padding: 10px 15px;
		padding-bottom: calc(10px + constant(safe-area-inset-bottom));
		padding-bottom: calc(10px + env(safe-area-inset-bottom));
		border-top: 1px solid #f0f0f0;
		box-sizing: border-box;
		z-index: 99;
	}
</style>
