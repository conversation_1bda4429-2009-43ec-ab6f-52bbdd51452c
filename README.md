# 容易购 Buy App

## 项目描述

这是一个使用 Uni-App 构建的跨平台应用程序，旨在促进电子商务或社交商务互动。它包含用户认证、产品浏览、店铺管理、社交圈和个人用户资料等功能。

## 使用技术

*   **框架:** Uni-App (Vue.js)
*   **状态管理:** Vuex
*   **UI 组件:** Uni-UI 组件 (来自 `uni_modules`)

## 项目结构

*   `api/`: 包含各种功能的 API 服务模块，如登录、产品管理和用户操作。
*   `components/`: 可重用的 Vue 组件。
*   `pages/`: 应用程序页面/视图，按功能组织（例如 `mine`、`productDetail`、`circle`）。
*   `plugins/`: 用于认证、模态框和选项卡管理等功能的 Vue 插件。
*   `static/`: 静态资源，如图片、字体和全局样式。
*   `store/`: 用于集中状态管理的 Vuex 存储模块。
*   `uni_modules/`: 第三方 Uni-App 组件和模块。
*   `utils/`: 用于常见任务（如请求、存储和权限）的实用函数。
*   `App.vue`: 主应用程序组件。
*   `main.js`: 应用程序入口文件。
*   `manifest.json`: Uni-App 清单文件，用于应用程序配置。
*   `pages.json`: Uni-App 页面配置和路由。
*   `uni.scss`: Uni-App 的全局 SCSS 样式。

## 快速开始

要本地设置和运行此项目，请按照以下步骤操作：

### 先决条件

*   Node.js (推荐 LTS 版本)
*   npm 或 yarn
*   HBuilderX (推荐的 Uni-App 开发 IDE)

### 安装

1.  克隆仓库：

    ```bash
    git clone <repository-url>
    cd buy-app
    ```

2.  安装依赖：

    ```bash
    npm install
    # 或 yarn install
    ```

### 运行应用程序

在 HBuilderX 中打开项目。然后，您可以使用 HBuilderX 中内置的运行命令在各种平台（例如，微信小程序、H5、iOS、Android）上运行应用程序。

或者，如果 `package.json` 中定义了 npm 脚本（如果不存在，您可能需要创建或配置这些脚本）：

*   **对于 H5 (Web):**

    ```bash
    npm run dev:h5
    # 或 npm run build:h5 (用于生产构建)
    ```

*   **对于微信小程序 (需要微信开发者工具):**

    ```bash
    npm run dev:mp-weixin
    # 或 npm run build:mp-weixin (用于生产构建)
    ```

（如果此处未列出，请参阅 Uni-App 文档以获取特定平台的构建命令。）

## 主要功能 (推断)

*   用户认证（登录、注册）
*   产品浏览和详情视图
*   店铺列表和管理
*   社交圈（帖子、评论、朋友）
*   用户资料管理（头像、个人信息、密码、设置）
*   收藏管理
*   隐私政策和用户协议视图
*   视频画廊