<template>
  <view class="login-container">
    <view class="welcome-text">欢迎使用</view>

    <view class="login-card">
      <!-- 协议勾选区域 -->
      <view class="agreement-section">
        <checkbox-group @change="agreementChange">
          <checkbox :checked="isAgreed" color="#dd1a21" style="transform:scale(0.8)" />
        </checkbox-group>
        <view class="agreement-text">
          <text>我已阅读并同意</text>
          <text class="link-text" @click.stop="handleUserAgrement">《用户协议》</text>
          <text>及</text>
          <text class="link-text" @click.stop="handlePrivacy">《隐私授权》</text>
        </view>
      </view>

      <!-- 微信登录按钮 -->
      <button 
        class="wechat-login-btn"
        :open-type="isAgreed ? 'getPhoneNumber' : ''"
        @getphonenumber="WeChatLogin"
        @click="checkAgreement"
      >
        微信授权登录
      </button>

      <view class="home-link" @click="goHome">返回首页</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        // 协议是否同意的标志
        isAgreed: false,
        // 全局配置，用于获取协议链接等
        globalConfig: getApp().globalData.config,
        // 邀请用户ID
        inviteUserId: null
      }
    },
    onLoad(options) {
      // 获取邀请用户ID参数
      if (options.inviteUserId) {
        this.inviteUserId = options.inviteUserId
        console.log('接收到邀请用户ID:', this.inviteUserId)
      }
    },
    methods: {
      // 监听checkbox状态改变
      agreementChange(e) {
        this.isAgreed = e.detail.value.length > 0;
      },
      // 查看隐私协议
      handlePrivacy() {
        uni.navigateTo({
        	url:"/pages/privacy/privacy"
        })
      },
      // 查看用户协议
      handleUserAgrement() {
        uni.navigateTo({
        	url:"/pages/userAgrement/userAgrement"
        })
      },
      
      /**
       * 修复后的核心逻辑：
       * 此方法绑定到 @click，用于在触发 open-type 之前检查协议。
       * 如果未同意，则只弹窗提示，不触发微信授权。
       */
      checkAgreement() {
        if (!this.isAgreed) {
          uni.showModal({
            title: '服务协议确认',
            content: '请先阅读并同意我们的用户协议和隐私政策，再进行登录。',
            confirmText: '同意',
            cancelText: '暂不',
            success: (res) => {
              if (res.confirm) {
                // 用户点击同意，自动帮他勾选
                this.isAgreed = true;
                // 提示用户可以再次点击登录了
                // this.$modal.msg('您已同意协议，请再次点击登录');
              }
            }
          });
        }
        // 如果 this.isAgreed 已经是 true，这个函数什么也不做，
        // 点击事件会直接触发 :open-type="getPhoneNumber" 的效果。
      },
      
      /**
       * 此方法绑定到 @getphonenumber，只在用户成功授权手机号后才会触发。
       */
      WeChatLogin(e) {
        // 再次确认，虽然理论上此时 isAgreed 必为 true
        if (!this.isAgreed) return;

        // 用户在微信授权弹窗中点击了拒绝
        if (!e.detail.code) {
          this.$modal.msgError("您取消了授权");
          return
        }

        this.$modal.loading("登录中，请耐心等待...")
        let params = {}
        params.code = e.detail.code

        // 如果有邀请用户ID，添加到登录参数中
        if (this.inviteUserId) {
          params.inviteUserId = this.inviteUserId
          console.log('登录时传递邀请用户ID:', this.inviteUserId)
        }

        // 获取用户信息（头像、昵称）
        uni.getUserInfo({
          success: (res) => {
            params.avatarUrl = res.userInfo.avatarUrl
            params.nickName = res.userInfo.nickName
          },
          // 无论是否成功获取用户信息，都继续执行登录
          complete: async () => {
            this.$store.dispatch('WxLogin', params).then(res => {
              this.loginSuccess()
            }).catch(() => {
              this.$modal.closeLoading()
            })
          }
        })
      },
      
      // 登录成功后的处理
      loginSuccess() {
        this.$modal.closeLoading()
        this.$store.dispatch('GetInfo').then(res => {
          this.$tab.reLaunch('/pages/index')
        })
      },
      // 返回首页
      goHome() {
        uni.switchTab({
          url: '/pages/index'
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background: linear-gradient(to bottom, #FEF0F0, #ffffff 40%);
    height: 100vh;
    overflow: hidden;
  }

  .login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 60rpx;
  }

  .welcome-text {
    font-size: 44rpx;
    font-weight: 500;
    color: #333333;
    padding-top: 20vh;
    margin-bottom: 80rpx;
  }

  .login-card {
    width: 100%;
    background-color: #ffffff;
    border-radius: 32rpx;
    padding: 60rpx 50rpx 50rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
  }
  
  .agreement-section {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 40rpx;
  }

  .agreement-text {
    font-size: 24rpx;
    color: #AAAAAA;
    
    .link-text {
      color: #555555;
    }
  }

  .wechat-login-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    background-color: #dd1a21;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 45rpx;

    &::after {
      border: none;
    }
  }
  
  .home-link {
    margin-top: 40rpx;
    font-size: 28rpx;
    color: #666666;
    text-decoration: underline;
  }
</style>