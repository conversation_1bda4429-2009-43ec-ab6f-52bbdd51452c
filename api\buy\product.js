import request from '@/utils/request'

// 查询商品信息列表
export function listProduct(query) {
  return request({
    url: '/buy/product/list',
    method: 'get',
    params: query
  })
}

// 查询商品信息详细
export function getProduct(productId) {
  return request({
    url: '/buy/product/' + productId,
    method: 'get'
  })
}

// 新增商品信息
export function addProduct(data) {
  return request({
    url: '/buy/product',
    method: 'post',
    data: data
  })
}

// 修改商品信息
export function updateProduct(data) {
  return request({
    url: '/buy/product',
    method: 'put',
    data: data
  })
}

// 删除商品信息
export function delProduct(productId) {
  return request({
    url: '/buy/product/' + productId,
    method: 'delete'
  })
}

// 获取促销商品列表
export function listPromotions(query) {
  return request({
    url: '/buy/product/promotions',
    method: 'get',
    params: query
  })
}
