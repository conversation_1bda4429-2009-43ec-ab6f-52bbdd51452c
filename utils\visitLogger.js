import { addSeeLog } from "@/api/buy/seeLog.js"
import store from "@/store"

/**
 * 访问记录工具类
 */
class VisitLogger {
	/**
	 * 记录访问记录
	 * @param {string|number} targetId - 目标ID
	 * @param {string} type - 访问类型：'0'店铺访问记录，'1'分类访问记录
	 * @param {string} description - 描述信息（可选，用于日志）
	 */
	static async recordVisit(targetId, type, description = '') {
		try {
			// 获取用户ID，如果未登录则为0
			let userId = 0;
			if (store.state.user && store.state.user.userId) {
				userId = store.state.user.userId;
			}
			
			const visitData = {
				userId: userId,
				targetId: targetId,
				type: type
			};
			
			// 异步记录访问，不影响页面加载
			const res = await addSeeLog(visitData);
			console.log(`${description}访问记录已记录:`, res);
			return res;
		} catch (error) {
			console.error(`记录${description}访问失败:`, error);
			throw error;
		}
	}
	
	/**
	 * 记录店铺访问
	 * @param {string|number} storeId - 店铺ID
	 */
	static recordStoreVisit(storeId) {
		return this.recordVisit(storeId, '0', '店铺');
	}
	
	/**
	 * 记录分类访问
	 * @param {string|number} categoryId - 分类ID
	 */
	static recordCategoryVisit(categoryId) {
		return this.recordVisit(categoryId, '1', '分类');
	}
}

export default VisitLogger;
