import request from '@/utils/request'

// 查询帖子评论列表
export function listCircleComment(query) {
  return request({
    url: '/buy/circleComment/list',
    method: 'get',
    params: query
  })
}

// 查询帖子评论详细
export function getCircleComment(commentId) {
  return request({
    url: '/buy/circleComment/' + commentId,
    method: 'get'
  })
}

// 新增帖子评论
export function addCircleComment(data) {
  return request({
    url: '/buy/circleComment',
    method: 'post',
    data: data
  })
}

// 修改帖子评论
export function updateCircleComment(data) {
  return request({
    url: '/buy/circleComment',
    method: 'put',
    data: data
  })
}

// 删除帖子评论
export function delCircleComment(commentId) {
  return request({
    url: '/buy/circleComment/' + commentId,
    method: 'delete'
  })
}
