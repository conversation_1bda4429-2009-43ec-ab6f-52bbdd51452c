<template>
	<view class="publish-container">
		<scroll-view class="content-scroll" scroll-y="true">
			<!-- 圈子选择 -->
			<view class="form-section">
				<view class="section-title">选择圈子</view>
				<view class="circle-selector" @click="showCirclePicker">
					<view class="selected-circle">
						<image 
							v-if="selectedCircle.avatar" 
							:src="getImageUrl(selectedCircle.avatar)" 
							class="circle-avatar"
							mode="aspectFill"
						></image>
						<view v-else class="circle-avatar default-avatar">
							<text class="avatar-text">{{ selectedCircle.name ? selectedCircle.name.charAt(0) : '选' }}</text>
						</view>
						<text class="circle-name">{{ selectedCircle.name || '请选择圈子' }}</text>
					</view>
					<text class="arrow">></text>
				</view>
			</view>
			
			<!-- 内容输入 -->
			<view class="form-section">
				<view class="section-title">帖子内容</view>
				<textarea 
					v-model="form.content"
					class="content-textarea"
					placeholder="分享你的想法..."
					:maxlength="-1"
					auto-height
				></textarea>
			</view>
			
			<!-- 图片上传 -->
			<view class="form-section">
				<view class="section-title">图片 (最多9张)</view>
				<view class="upload-container">
					<view class="image-list">
						<view 
							v-for="(image, index) in imageList" 
							:key="index"
							class="image-item"
						>
							<image 
								:src="image.url" 
								class="upload-image"
								mode="aspectFill"
							></image>
							<view class="delete-btn" @click="deleteImage(index)">
								<text class="delete-icon">×</text>
							</view>
							<view v-if="image.status === 'uploading'" class="upload-mask">
								<view class="loading-spinner"></view>
							</view>
						</view>
						<view 
							v-if="imageList.length < 9" 
							class="upload-btn"
							@click="chooseImage"
						>
							<text class="upload-icon">+</text>
							<text class="upload-text">添加图片</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 视频上传 -->
			<view class="form-section">
				<view class="section-title">视频 (最多1个)</view>
				<view class="upload-container">
					<view v-if="videoInfo.url" class="video-item">
						<video 
							:src="videoInfo.url"
							class="upload-video"
							controls
						></video>
						<view class="delete-btn" @click="deleteVideo">
							<text class="delete-icon">×</text>
						</view>
						<view v-if="videoInfo.status === 'uploading'" class="upload-mask">
							<view class="loading-spinner"></view>
						</view>
					</view>
					<view 
						v-else 
						class="upload-btn video-upload-btn"
						@click="chooseVideo"
					>
						<text class="upload-icon">📹</text>
						<text class="upload-text">添加视频</text>
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<button class="cancel-btn" @click="goBack">取消</button>
			<button class="publish-btn" :disabled="!canPublish" @click="publishPost">发布</button>
		</view>
		
		<!-- 圈子选择弹窗 -->
		<view v-if="showPicker" class="picker-mask" @click="showPicker = false">
			<view class="picker-container" @click.stop>
				<view class="picker-header">
					<text class="picker-title">选择圈子</text>
					<text class="picker-close" @click="showPicker = false">×</text>
				</view>
				<scroll-view class="picker-content" scroll-y="true">
					<view
						v-for="circle in circleList"
						:key="circle.circleId"
						class="picker-item"
						:class="{ active: selectedCircle.circleId === circle.circleId }"
						@click="selectCircle(circle)"
					>
						<image
							v-if="circle.avatar"
							:src="getImageUrl(circle.avatar)"
							class="picker-avatar"
							mode="aspectFill"
						></image>
						<view v-else class="picker-avatar default-avatar">
							<text class="avatar-text">{{ circle.name.charAt(0) }}</text>
						</view>
						<text class="picker-name">{{ circle.name }}</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
import { listCircle } from '@/api/buy/circle'
import { addCirclePost } from '@/api/buy/circlePost'
import config from '@/config'
import { getToken } from '../../../utils/auth'

export default {
	data() {
		return {
			form: {
				circleId: null,
				content: '',
				images: '',
				videoUrl: ''
			},
			selectedCircle: {},
			circleList: [],
			imageList: [],
			videoInfo: {},
			showPicker: false,
			uploading: false,
			baseUrl: config.baseUrl,
			uploadUrl: ''
		}
	},
	computed: {
		canPublish() {
			return this.form.circleId && 
				   (this.form.content.trim() || this.imageList.length > 0 || this.videoInfo.url) &&
				   !this.uploading;
		}
	},
	onLoad(options) {
		// 初始化上传URL
		this.initUploadUrl();
		
		// 获取圈子列表
		this.getCircleList();
		
		// 如果传入了圈子ID，预选择该圈子
		if (options.circleId) {
			this.form.circleId = options.circleId;
		}
	},
	methods: {
		// 初始化上传URL
		initUploadUrl() {
			const baseUrl = process.env.VUE_APP_BASE_API;
			if (baseUrl && baseUrl !== 'undefined' && baseUrl.trim() !== '') {
				this.uploadUrl = baseUrl + '/common/upload';
			} else {
				try {
					const config = require('@/config.js');
					if (config && config.baseUrl) {
						this.uploadUrl = config.baseUrl + '/common/upload';
					} else {
						this.uploadUrl = 'http://localhost:8080/common/upload';
					}
				} catch (error) {
					this.uploadUrl = 'http://localhost:8080/common/upload';
				}
			}
		},
		
		// 获取圈子列表
		async getCircleList() {
			try {
				const response = await listCircle({});
				if (response.code === 200) {
					this.circleList = response.rows || [];
					
					// 如果有预选择的圈子ID，找到对应的圈子
					if (this.form.circleId) {
						const circle = this.circleList.find(c => c.circleId == this.form.circleId);
						if (circle) {
							this.selectedCircle = circle;
						}
					}
				}
			} catch (error) {
				console.error('获取圈子列表失败:', error);
			}
		},
		
		// 显示圈子选择器
		showCirclePicker() {
			this.showPicker = true;
		},
		
		// 选择圈子
		selectCircle(circle) {
			this.selectedCircle = circle;
			this.form.circleId = circle.circleId;
			this.showPicker = false;
		},
		
		// 获取图片URL
		getImageUrl(imagePath) {
			if (!imagePath) return '';
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				return imagePath;
			}
			return this.baseUrl + imagePath;
		},
		
		// 选择图片
		chooseImage() {
			const remainCount = 9 - this.imageList.length;
			uni.chooseImage({
				count: remainCount,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadImages(res.tempFilePaths);
				}
			});
		},
		
		// 上传图片
		async uploadImages(filePaths) {
			this.uploading = true;
			
			for (let filePath of filePaths) {
				// 添加到列表中显示上传状态
				const imageItem = {
					url: filePath,
					status: 'uploading',
					fileName: ''
				};
				this.imageList.push(imageItem);
				
				try {
					const uploadResult = await this.uploadFile(filePath);
					if (uploadResult.code === 200) {
						imageItem.status = 'success';
						imageItem.fileName = uploadResult.fileName;
						imageItem.url = this.baseUrl + uploadResult.fileName;
					} else {
						imageItem.status = 'failed';
						uni.showToast({
							title: '图片上传失败',
							icon: 'none'
						});
					}
				} catch (error) {
					imageItem.status = 'failed';
					console.error('图片上传失败:', error);
				}
			}
			
			this.uploading = false;
			this.updateFormImages();
		},
		
		// 删除图片
		deleteImage(index) {
			this.imageList.splice(index, 1);
			this.updateFormImages();
		},
		
		// 更新表单图片字段
		updateFormImages() {
			this.form.images = this.imageList
				.filter(item => item.status === 'success')
				.map(item => item.fileName)
				.join(',');
		},
		
		// 选择视频
		chooseVideo() {
			uni.chooseVideo({
				sourceType: ['album', 'camera'],
				maxDuration: 60,
				success: (res) => {
					this.uploadVideo(res.tempFilePath);
				}
			});
		},
		
		// 上传视频
		async uploadVideo(filePath) {
			this.uploading = true;
			this.videoInfo = {
				url: filePath,
				status: 'uploading',
				fileName: ''
			};
			
			try {
				const uploadResult = await this.uploadFile(filePath);
				if (uploadResult.code === 200) {
					this.videoInfo.status = 'success';
					this.videoInfo.fileName = uploadResult.fileName;
					this.videoInfo.url = this.baseUrl + uploadResult.fileName;
					this.form.videoUrl = uploadResult.fileName;
				} else {
					this.videoInfo.status = 'failed';
					uni.showToast({
						title: '视频上传失败',
						icon: 'none'
					});
				}
			} catch (error) {
				this.videoInfo.status = 'failed';
				console.error('视频上传失败:', error);
			}
			
			this.uploading = false;
		},
		
		// 删除视频
		deleteVideo() {
			this.videoInfo = {};
			this.form.videoUrl = '';
		},
		
		// 上传文件
		uploadFile(filePath) {
			return new Promise((resolve, reject) => {
				const token = getToken();
				uni.uploadFile({
					url: this.uploadUrl,
					filePath: filePath,
					name: 'file',
					header: {
						'Authorization': 'Bearer ' + token
					},
					success: (res) => {
						try {
							const data = JSON.parse(res.data);
							resolve(data);
						} catch (e) {
							reject(e);
						}
					},
					fail: reject
				});
			});
		},
		
		// 发布帖子
		async publishPost() {
			if (!this.canPublish) return;
			
			try {
				uni.showLoading({ title: '发布中...' });
				
				const response = await addCirclePost(this.form);
				if (response.code === 200) {
					uni.showToast({
						title: '发布成功',
						icon: 'success'
					});
					
					// 返回上一页并刷新数据
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({
						title: response.msg || '发布失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('发布失败:', error);
				uni.showToast({
					title: '发布失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss">
.publish-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
}

.content-scroll {
	flex: 1;
	padding: 20rpx;
	padding-bottom: calc(140rpx + env(safe-area-inset-bottom)); /* 为底部操作栏和安全区域留出空间 */
}

.form-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	
	.section-title {
		font-size: 28rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 20rpx;
	}
}

.circle-selector {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	
	.selected-circle {
		display: flex;
		align-items: center;
		
		.circle-avatar {
			width: 60rpx;
			height: 60rpx;
			border-radius: 30rpx;
			margin-right: 16rpx;
			
			&.default-avatar {
				background-color: #2979ff;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.avatar-text {
					color: #fff;
					font-size: 24rpx;
					font-weight: 500;
				}
			}
		}
		
		.circle-name {
			font-size: 28rpx;
			color: #333;
		}
	}
	
	.arrow {
		font-size: 32rpx;
		color: #999;
	}
}

.content-textarea {
	width: 100%;
	min-height: 200rpx;
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	background-color: transparent;
	border: none;
	outline: none;
}

.upload-container {
	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		
		.image-item {
			position: relative;
			width: 200rpx;
			height: 200rpx;
			
			.upload-image {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}
			
			.delete-btn {
				position: absolute;
				top: -10rpx;
				right: -10rpx;
				width: 40rpx;
				height: 40rpx;
				background-color: #ff4757;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.delete-icon {
					color: #fff;
					font-size: 24rpx;
					font-weight: bold;
				}
			}
			
			.upload-mask {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: rgba(0, 0, 0, 0.5);
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.loading-spinner {
					width: 40rpx;
					height: 40rpx;
					border: 4rpx solid #fff;
					border-top: 4rpx solid transparent;
					border-radius: 50%;
					animation: spin 1s linear infinite;
				}
			}
		}
		
		.upload-btn {
			width: 200rpx;
			height: 200rpx;
			border: 2rpx dashed #ddd;
			border-radius: 8rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: #fafafa;
			
			.upload-icon {
				font-size: 48rpx;
				color: #999;
				margin-bottom: 8rpx;
			}
			
			.upload-text {
				font-size: 24rpx;
				color: #999;
			}
			
			&.video-upload-btn {
				width: 100%;
				height: 300rpx;
			}
		}
	}
	
	.video-item {
		position: relative;
		
		.upload-video {
			width: 100%;
			height: 300rpx;
			border-radius: 8rpx;
		}
		
		.delete-btn {
			position: absolute;
			top: 10rpx;
			right: 10rpx;
			width: 40rpx;
			height: 40rpx;
			background-color: rgba(0, 0, 0, 0.6);
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.delete-icon {
				color: #fff;
				font-size: 24rpx;
				font-weight: bold;
			}
		}
		
		.upload-mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(0, 0, 0, 0.5);
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.loading-spinner {
				width: 40rpx;
				height: 40rpx;
				border: 4rpx solid #fff;
				border-top: 4rpx solid transparent;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
		}
	}
}

.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	padding: 20rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	background-color: #fff;
	border-top: 1px solid #e5e5e5;
	gap: 20rpx;
	z-index: 999;

	.cancel-btn {
		flex: 1;
		height: 80rpx;
		background-color: #f5f5f5 !important;
		color: #666 !important;
		border: 2rpx solid #ddd !important;
		border-radius: 8rpx;
		font-size: 28rpx;
		line-height: 80rpx;
		text-align: center;

		&::after {
			border: none !important;
		}

		&:active {
			background-color: #e8e8e8 !important;
			border-color: #ccc !important;
		}
	}

	.publish-btn {
		flex: 2;
		height: 80rpx;
		background-color: #2979ff !important;
		color: #fff !important;
		border: none !important;
		border-radius: 8rpx;
		font-size: 28rpx;
		line-height: 80rpx;
		text-align: center;

		&::after {
			border: none !important;
		}

		&:disabled {
			background-color: #ccc !important;
			color: #999 !important;
		}

		&:not(:disabled):active {
			background-color: #1e5bb8 !important;
		}
	}
}

.picker-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: flex-end;
}

.picker-container {
	width: 100%;
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	animation: slideUp 0.3s ease-out;

	.picker-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1px solid #f0f0f0;

		.picker-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}

		.picker-close {
			font-size: 40rpx;
			color: #999;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.picker-content {
		max-height: 60vh;

		.picker-item {
			display: flex;
			align-items: center;
			padding: 24rpx 30rpx;
			border-bottom: 1px solid #f8f9fa;

			&.active {
				background-color: #f0f8ff;

				.picker-name {
					color: #2979ff;
					font-weight: 500;
				}
			}

			.picker-avatar {
				width: 60rpx;
				height: 60rpx;
				border-radius: 30rpx;
				margin-right: 20rpx;

				&.default-avatar {
					background-color: #2979ff;
					display: flex;
					align-items: center;
					justify-content: center;

					.avatar-text {
						color: #fff;
						font-size: 24rpx;
						font-weight: 500;
					}
				}
			}

			.picker-name {
				font-size: 28rpx;
				color: #333;
			}
		}
	}
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
